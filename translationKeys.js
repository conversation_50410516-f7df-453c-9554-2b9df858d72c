module.exports = [
  "j-agent-web_pages_category_components_CategorySelect_NoCategoryDataIsAvailable",
  "j-agent-web_pages_category_components_CategorySelect_Reset",
  "j-agent-web_pages_category_components_CategorySelect_Confirm",
  "j-agent-web_pages_category_LevelCategory",
  "j-agent-web_pages_category_ApplyFilterCriteria",
  "j-agent-web_pages_category_components_CategorySelect_LevelCategory",
  "j-agent-web_pages_category_components_CategorySelect_SecondaryCategory",
  "j-agent-web_pages_category_components_CategorySelect_LevelCategory_1",
  "j-agent-web_pages_category_ClosedSuccessfully",
  "j-agent-web_pages_category_FailedToClose",
  "j-agent-web_pages_comments_CurrentlyScoringIsNotSupported",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CommentsDatacommdesc",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentFeatures",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_SalesList",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_DailySalesList",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_WeeklySalesList",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_MonthlySalesList",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_Comments",
  "j-agent-web_utils_util_AYearAgo",
  "j-agent-web_utils_util_YearsYearsAgo",
  "j-agent-web_utils_util_AMonthAgo",
  "j-agent-web_utils_util_MonthsMonthsAgo",
  "j-agent-web_utils_util_ADayAgo",
  "j-agent-web_utils_util_DaysDaysAgo",
  "j-agent-web_utils_util_AnHourAgo",
  "j-agent-web_utils_util_HoursHoursAgo",
  "j-agent-web_utils_util_OneMinuteAgo",
  "j-agent-web_utils_util_MinutesMinutesAgo",
  "j-agent-web_utils_util_JustNow",
  "j-agent-web_utils_util_SecondsSecondsAgo",
  "j-agent-web_pages_commodityDetail_components_ProductPage_SorryThereIsNoProduct",
  "j-agent-web_pages_category_SubscriptionSuccessful",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadFailed",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadedSuccessfully",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFailed",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFiles",
  "j-agent-web_pages_agentForm_components_FileUploader_SupportedFormatPhtASingle",
  "j-agent-web_pages_agentForm_components_FileUploader_Files",
  "j-agent-web_pages_agentForm_components_FileUploader_PreviewImage",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelId",
  "j-agent-web_pages_agentForm_components_FormComponent_Copy",
  "j-agent-web_pages_agentForm_components_FormComponent_CopiedSuccessfully",
  "j-agent-web_pages_agentForm_components_FormComponent_UpdatedSuccessfully",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUploadFailedErr",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionError",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionFailedPleaseTry",
  "j-agent-web_pages_agentForm_components_FormComponent_TheFormHasBeenReset",
  "j-agent-web_pages_agentForm_components_FormComponent_SelectAModelToUpdate",
  "j-agent-web_pages_agentForm_components_FormComponent_TheModelToBeUpdated",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelName",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterAModelName",
  "j-agent-web_pages_agentForm_components_FormComponent_UploadModelFiles",
  "j-agent-web_pages_agentForm_components_FormComponent_PromptEditingSupportsMarkdown",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterPrompt",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpdate",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpload",
  "j-agent-web_pages_agentForm_components_FormComponent_Reset",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Bold",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Italic",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_OrderedList",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_UnorderedList",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Link",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Edit",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_MarkdownFormatEditingIsSupported",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Preview",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_NoContentPreview",
  "j-agent-web_pages_agentForm_AddModel",
  "j-agent-web_pages_agentForm_UpdateModelPrompt",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainOssConfiguration",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainUploadCredentials",
  "j-agent-web_pages_agentForm_services_ossService_FailedToUploadTheFile",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMost",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostDaily",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostWeekly",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostMonthly",
  "j-agent-web_pages_cardTips_NewProductsWithTheLargest",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldThe",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldAll",
  "j-agent-web_pages_cardTips_DataDescription",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_StoreNameProductstorename",
  "j-agent-web_pages_productCuration_components_ProductList_Rating",
  "j-agent-web_pages_productCuration_ProductSelectionCenter",
  "j-agent-web_pages_productCuration_UnableToObtainBizid",
  "j-agent-web_pages_productCuration_FailedToObtainProductList",
  "j-agent-web_pages_productCuration_FailedToSubmitTheProduct",
  "j-agent-web_pages_productCuration_LoadingGoods",
  "j-agent-web_pages_productCuration_ProductslengthItemsInTotalSelectedcount",
  "j-agent-web_pages_productCuration_DeselectAll",
  "j-agent-web_pages_productCuration_SelectAllCurrentPage",
  "j-agent-web_pages_productCuration_NoProductDataAvailable",
  "j-agent-web_pages_productCuration_SubmitSelectionSelectedcount",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing",
  "j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData",
  "j-agent-web_pages_productOrigin_components_ProductPage_ZozoGrid",
  "j-agent-web_pages_productOrigin_components_ProductPage_AmazonGrid",
  "j-agent-web_pages_productOrigin_components_ProductPage_RednoteGrid",
  "j-agent-web_pages_productOrigin_components_ProductPage_TiktokGrid",
  "j-agent-web_pages_productOrigin_components_ProductPage_TaobaoGrid",
  "j-agent-web_pages_productOrigin_components_ProductPage_AlibabaGrid",
  "j-agent-web_pages_productOrigin_components_ProductPage_Details",
  "j-agent-web_pages_productOrigin_components_ProductPage_Favorites",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductdatafavnumPeople",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoCollection",
  "j-agent-web_pages_productOrigin_components_ProductPage_List",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductRating",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoScore",
  "j-agent-web_pages_productOrigin_components_ProductPage_SimilarProducts",
  "j-agent-web_pages_category_components_CategorySelect_AllCategories",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_FindSimilarity",
  "j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfLikes",
  "j-agent-web_pages_productOriginV2_components_ProductPage_DailyGrowth",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfDaysOnThe",
  "j-agent-web_pages_productOriginV2_components_ProductPage_SalesVolume",
  "j-agent-web_pages_productOriginV2_components_ProductPage_MonthlySales",
  "j-agent-web_pages_productOriginV2_components_ProductPage_RepurchaseRate",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ProductEvaluation",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ListSource",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NoListInformation",
  "j-agent-web_pages_productOriginV2_components_ProductPage_FactoryPrice",
  "j-agent-web_pages_productCuration_components_ProductList_ViewProductDetails",
  "j-agent-web_pages_productOrigin_components_ProductPage_ShareContent",
  "j-agent-web_pages_productCuration_SuccessfullySubmitted",
  "j-dingtalk-web_components_ImageSwiper_ProductPortrait",
  "j-dingtalk-web_components_OptimizedImage_PortraitFailure",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_ForProductInformationSee",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pieces",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ProductPortrait",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NoMore",
  "j-dingtalk-web_pages_category_FailedToObtainCategoryData",
  "j-dingtalk-web_pages_category_SelectASecondaryCategory",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_MoreProducts",
  "j-dingtalk-web_pages_category_CategorySelectionSucceeded",
  "j-dingtalk-web_pages_category_CategorySelectionFailed",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_MoreDetails",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CommentDetails",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentCount",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_StarRating",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_ScoreFullScore",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CustomerFeedback",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_GeneratedByAiBasedOn",
  "j-agent-web_pages_productOrigin_components_ProductPage_Price",
  "j-dingtalk-web_pages_premiumStore_StoreDetails",
  "j-dingtalk-web_pages_premiumStore_ComprehensiveEvaluation",
  "j-dingtalk-web_pages_premiumStore_ProductQuality",
  "j-dingtalk-web_pages_premiumStore_DeliverySpeed",
  "j-dingtalk-web_pages_premiumStore_ConsultingServices",
  "j-dingtalk-web_pages_premiumStore_SimilarProducts",
  "j-dingtalk-web_pages_topMerchant_GoodNumberDetails",
  "j-dingtalk-web_pages_topMerchant_FailedToObtainData",
  "j-dingtalk-web_pages_topMerchant_Fans",
  "j-dingtalk-web_pages_topMerchant_NumberOfInteractions",
  "j-dingtalk-web_pages_topMerchant_DataOverview",
  "j-dingtalk-web_pages_topMerchant_NumberOfLikesReceived",
  "j-dingtalk-web_pages_topMerchant_NumberOfNotes",
  "j-dingtalk-web_pages_topMerchant_NumberOfCooperativeBrands",
  "j-dingtalk-web_pages_topMerchant_Mockmerchantinfobrandcount",
  "j-dingtalk-web_pages_topMerchant_NumberOfConcerns",
  "j-dingtalk-web_pages_topMerchant_StyleOverview",
  "j-dingtalk-web_pages_topMerchant_HighestInteraction",
  "j-dingtalk-web_pages_topMerchant_RecentlyReleased",
  "j-dingtalk-web_pages_topMerchant_TheGoodsMentionedInThis",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts",
  "j-dingtalk-web_pages_topMerchant_ProductsSoldByTa",
  "j-dingtalk-web_pages_topMerchant_HighestSalesVolume",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts_1",
  "j-dingtalk-web_components_CompositionEditor_Title",
  "j-dingtalk-web_components_CompositionEditor_Cancel",
  "j-dingtalk-web_components_CompositionEditor_Export",
  "j-dingtalk-web_components_CompositionEditor_ExportSuccess",
  "j-dingtalk-web_components_CompositionEditor_ExportFailed",
  "j-dingtalk-web_components_CompositionEditor_OpenEditor",
  "j-dingtalk-web_components_CompositionEditor_Reset",
  "j-dingtalk-web_components_CompositionEditor_ResetSuccess"
]