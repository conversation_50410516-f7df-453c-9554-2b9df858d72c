import { useCallback, useRef, useState } from 'react';

/**
 * Generic polling configuration
 */
export interface PollingConfig<T = any> {
  /** Maximum number of polling attempts */
  maxAttempts?: number;
  /** Polling interval in milliseconds */
  interval?: number;
  /** Custom check function that returns the polling result */
  checkFunction: () => Promise<T>;
  /** Function to determine if polling should continue based on the result */
  shouldContinue: (result: T) => boolean;
  /** Function to determine if polling was successful */
  isSuccess: (result: T) => boolean;
  /** Function to determine if polling failed */
  isFailed: (result: T) => boolean;
  /** Function to get error message from result */
  getErrorMessage: (result: T) => string;
  /** Callback when polling starts */
  onStart?: () => void;
  /** Callback when polling succeeds */
  onSuccess?: (result: T) => void;
  /** Callback when polling fails */
  onError?: (error: string) => void;
  /** Callback for each polling attempt */
  onProgress?: (attempt: number, maxAttempts: number) => void;
  /** Callback when polling is cancelled */
  onCancel?: () => void;
}

/**
 * Polling result interface
 */
export interface PollingResult<T = any> {
  /** Whether polling is currently active */
  isPolling: boolean;
  /** Current progress percentage (0-100) */
  progress: number;
  /** Current attempt number */
  currentAttempt: number;
  /** Final result when polling completes successfully */
  result: T | null;
  /** Error message if polling failed */
  error: string | null;
  /** Start polling function */
  startPolling: () => Promise<void>;
  /** Cancel polling function */
  cancelPolling: () => void;
  /** Reset polling state */
  reset: () => void;
}

/**
 * Generic polling hook for handling asynchronous polling tasks
 * Supports video generation, watermarking, merging, etc.
 */
export function usePolling<T = any>(config: PollingConfig<T>): PollingResult<T> {
  const [isPolling, setIsPolling] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentAttempt, setCurrentAttempt] = useState(0);
  const [result, setResult] = useState<T | null>(null);
  const [error, setError] = useState<string | null>(null);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isCancelledRef = useRef(false);

  const {
    maxAttempts = 60,
    interval = 1000,
    checkFunction,
    shouldContinue,
    isSuccess,
    isFailed,
    getErrorMessage,
    onStart,
    onSuccess,
    onError,
    onProgress,
    onCancel,
  } = config;

  const reset = useCallback(() => {
    setIsPolling(false);
    setProgress(0);
    setCurrentAttempt(0);
    setResult(null);
    setError(null);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    isCancelledRef.current = false;
  }, []);

  const cancelPolling = useCallback(() => {
    isCancelledRef.current = true;
    setIsPolling(false);
    setProgress(0);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    onCancel?.();
  }, [onCancel]);

  const startPolling = useCallback(async (): Promise<void> => {
    if (isPolling) {
      return;
    }

    reset();
    setIsPolling(true);
    onStart?.();

    const poll = async (attempt: number): Promise<void> => {
      if (isCancelledRef.current) {
        return;
      }

      try {
        setCurrentAttempt(attempt);

        // Update progress (cap at 95% until completion)
        const progressPercent = Math.min(Math.round((attempt / maxAttempts) * 100), 95);
        setProgress(progressPercent);

        onProgress?.(attempt, maxAttempts);

        const pollResult = await checkFunction();

        // Check if polling should continue
        if (shouldContinue(pollResult)) {
          if (attempt >= maxAttempts) {
            throw new Error('操作超时，请稍后查看结果');
          }

          // Continue polling
          timeoutRef.current = setTimeout(() => {
            poll(attempt + 1);
          }, interval);
          return;
        }

        // Polling completed
        if (isSuccess(pollResult)) {
          setResult(pollResult);
          setProgress(100);
          setIsPolling(false);
          onSuccess?.(pollResult);
        } else if (isFailed(pollResult)) {
          const errorMsg = getErrorMessage(pollResult) || '操作失败';
          throw new Error(errorMsg);
        } else {
          throw new Error('未知的响应状态');
        }
      } catch (error) {
        console.error('Polling error:', error);
        setIsPolling(false);
        setProgress(0);
        const errorMessage = error instanceof Error ? error.message : '操作失败，请重试';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    };

    // Start polling
    poll(1);
  }, [
    isPolling,
    reset,
    onStart,
    maxAttempts,
    interval,
    checkFunction,
    shouldContinue,
    isSuccess,
    isFailed,
    getErrorMessage,
    onProgress,
    onSuccess,
    onError,
  ]);

  return {
    isPolling,
    progress,
    currentAttempt,
    result,
    error,
    startPolling,
    cancelPolling,
    reset,
  };
}
