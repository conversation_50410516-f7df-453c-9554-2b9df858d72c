import { useCallback, useRef } from 'react';

/**
 * Custom hook for debouncing function calls
 * @param callback - The function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 */
export const useDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;

  return debouncedCallback;
};

/**
 * Custom hook for debouncing async function calls with loading state
 * @param callback - The async function to debounce
 * @param delay - Delay in milliseconds
 * @returns Object with debounced function and loading state
 */
export const useAsyncDebounce = <T extends (...args: any[]) => Promise<any>>(
  callback: T,
  delay: number = 300
) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isExecutingRef = useRef(false);

  const debouncedCallback = useCallback(
    async (...args: Parameters<T>) => {
      // If already executing, ignore the call
      if (isExecutingRef.current) {
        return;
      }

      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout
      return new Promise<Awaited<ReturnType<T>>>((resolve, reject) => {
        timeoutRef.current = setTimeout(async () => {
          try {
            isExecutingRef.current = true;
            const result = await callback(...args);
            resolve(result);
          } catch (error) {
            reject(error);
          } finally {
            isExecutingRef.current = false;
          }
        }, delay);
      });
    },
    [callback, delay]
  ) as T;

  return {
    debouncedCallback,
    isExecuting: isExecutingRef.current,
  };
};
