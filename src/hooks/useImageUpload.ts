import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { Toast } from 'dingtalk-design-mobile';
import uploadWebImage from '@ali/dd-upload';
import { isMobileDevice } from '@/utils/jsapi';
import { sendUT } from '@/utils/trace';
import request from '@/apis/base';
import { validateImageSize, ImageSizeConstraints } from '@/utils/imageValidation';

interface UseImageUploadReturn {
  uploadImage: (imageDataUrl: string, filename?: string, sizeConstraints?: ImageSizeConstraints) => Promise<string | null>;
  loading: boolean;
  error: string | null;
}

const MAX_FILE_SIZE = 30 * 1024 * 1024;
const DEFAULT_FILENAME = 'image.png';
const COMPOSITION_FILENAME_PREFIX = 'composition_';

const useImageUpload = (): UseImageUploadReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isMountedRef = useRef(true);

  const deviceType = useMemo(() => isMobileDevice() ? 'mobile' : 'pc', []);

  const dataURLtoFile = useCallback((dataUrl: string, filename: string = DEFAULT_FILENAME): File => {
    if (!dataUrl || typeof dataUrl !== 'string') {
      throw new Error('Invalid data URL format');
    }

    const [header, data] = dataUrl.split(',');
    if (!header || !data) {
      throw new Error('Invalid data URL structure');
    }

    const mimeMatch = header.match(/:(.*?);/);
    const mime = mimeMatch?.[1] || 'image/png';

    if (!mime.startsWith('image/')) {
      throw new Error(`Invalid MIME type: ${mime}`);
    }

    const binaryString = atob(data);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const file = new File([bytes], filename, { type: mime });

    if (file.size === 0) {
      throw new Error('Generated file is empty');
    }

    return file;
  }, []);

  const uploadToWeb = useCallback(async (file: File): Promise<any> => {
    return uploadWebImage({
      file,
      lwp: true,
      lwpRequest: (url, headers, body) => request(url, body, headers),
      id: 'dingRichTextEditor',
      downloadId: 'dingRichTextEditor',
    });
  }, []);

  const showToast = useCallback((type: 'success' | 'fail', key: string, duration = type === 'success' ? 2 : 3) => {
    const content = i18next.t(key);
    Toast[type]({
      content,
      position: 'top',
      maskClickable: true,
      duration,
    });
  }, []);

  const uploadImage = useCallback(async (
    imageDataUrl: string, 
    filename: string = `${COMPOSITION_FILENAME_PREFIX}${Date.now()}.png`,
    sizeConstraints?: ImageSizeConstraints
  ): Promise<string | null> => {
    if (!isMountedRef.current) return null;

    setLoading(true);
    setError(null);

    try {
      const file = dataURLtoFile(imageDataUrl, filename);

      if (file.size > MAX_FILE_SIZE) {
        const errorMsg = i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_FileTooLarge');
        setError(errorMsg);
        showToast('fail', 'j-dingtalk-web_pages_aiVideo_components_ImageUploader_FileTooLarge');
        return null;
      }

      sendUT('aigc_picture_composition_upload', {
        device: deviceType,
        fileSize: file.size,
        filename,
      });

      const uploadResult = await uploadToWeb(file);

      if (uploadResult?.originUrl) {
        let finalUrl = uploadResult.originUrl;

        if (sizeConstraints) {
          try {
            const validationResult = await validateImageSize(uploadResult.originUrl, sizeConstraints);
            if (validationResult.isValid) {
              finalUrl = validationResult.processedUrl;
            }
          } catch {
            // Continue with original URL if validation fails
          }
        }

        sendUT('aigc_picture_composition_upload_success', {
          device: deviceType,
          imageUrl: finalUrl,
        });

        showToast('success', 'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully');
        return finalUrl;
      } else {
        throw new Error(`Upload failed: No URL returned. Result: ${JSON.stringify(uploadResult)}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);

      sendUT('aigc_picture_composition_upload_error', {
        device: deviceType,
        error: errorMessage,
      });

      showToast('fail', 'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed');
      return null;
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [dataURLtoFile, uploadToWeb, showToast, deviceType]);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    uploadImage,
    loading,
    error,
  };
};

export default useImageUpload;