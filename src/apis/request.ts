import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { getFluxionDomain } from '@/utils/env';

// 公共请求头
const authHeaders = {
  'API-KEY': 'Y85AWgoG45swao4FswVZWc99OWOn7fW9',
  'Authorization': 'Bearer Y85AWgoG45swao4FswVZWc99OWOn7fW9',
};

/**
 * Base request function with common logic
 * @param {string} endpoint - API endpoint path
 * @param {string} method - HTTP method
 * @param {any} data - Request data
 * @param {Record<string, string>} headers - Request headers
 * @returns {Promise<AxiosResponse>} - Axios response promise
 */
function baseRequest(
  endpoint: string,
  method = 'GET',
  data: any = {},
  headers: Record<string, string> = {},
): Promise<AxiosResponse> {
  let url = `${endpoint}`;

  const config: AxiosRequestConfig = {
    url,
    method,
    headers,
  };

  // Handle data based on method type
  if (method.toUpperCase() === 'GET') {
    // For GET requests, append parameters to URL
    if (data && Object.keys(data).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        url += `?${queryString}`;
        config.url = url;
      }
    }
  }

  // For non-GET requests, use request body
  if (method.toUpperCase() !== 'GET' && data) {
    config.data = data;
  }

  return axios(config);
}

// 图片搜索
export const queryProductByImage = (data: Record<string, any> = {}) => {
  return baseRequest('/v1/api/sourcing', 'GET', data);
};

// 举报假冒伪劣
export const reportProduct = (data: Record<string, any> = {}) => {
  return baseRequest('/v1/api/sourcing/product/report', 'POST', data);
};

// 下载文件
export const downloadFileByUrl = (data: Record<string, any> = {}) => {
  return baseRequest(`${getFluxionDomain()}/v1/oss/file/downloadFileByUrl`, 'GET', data, authHeaders);
};

// 批量下载文件
export const batchDownloadByUrls = (data: Record<string, any> = {}) => {
  return baseRequest(`${getFluxionDomain()}/v1/oss/file/batchDownloadByUrls`, 'POST', data, authHeaders);
};

// Export the base request function for custom usage
export { baseRequest };
