import request from './base';

// Generate video request parameters interface
export interface GenerateVideoRequest {
  imageInfo: string;
  positivePrompt?: string;
  negativePrompt?: string;
  quality?: string;
  duration?: number;
  [key: string]: any;
}

// Check video status request parameters interface
export interface CheckVideoStatusRequest {
  uuid: string;
}

// List videos request parameters interface
export interface ListVideosRequest {
  limit: number;
  nextUuid?: string;
  [key: string]: any;
}

// Generate GIF request parameters interface
export interface GenerateGifRequest {
  uuid: string;
  [key: string]: any;
}

// Check GIF status request parameters interface
export interface CheckGifStatusRequest {
  uuid: string;
}

// Regenerate video request parameters interface
export interface ReGenerateVideoRequest {
  fromUuid: string;
  requestId?: string;
  requirements?: string;
  [key: string]: any;
}

// Rate video request parameters interface
export interface RateVideoRequest {
  uuid: string;
  rating: 0 | 1 | -1;
}

// Upload image request parameters interface
export interface UploadImageRequest {
  file: File | Blob;
  [key: string]: any;
}

// Remove video request parameters interface
export interface RemoveVideoRequest {
  uuid: string;
}

export interface GenerateMultiVideoRequest {
  imageInfo: string;
  requirements: string;
  restrictions: string;
  quality: string;
  duration: number;
  [key: string]: any;
}

export interface GenerateMultiVideoResponse {
  success: boolean;
  uuid: string;
  errorMsg: string;
  [key: string]: any;
}

export interface VideoStatusInfo {
  requestId: string;
  status: 'pending' | 'processing' | 'finish' | 'failed';
  videoUrl?: string;
  finishTime?: number;
  duration: number;
};

export interface CheckMultiVideoStatusResponse {
  success: boolean;
  status: 'pending' | 'processing' | 'finish' | 'failed';
  uuid: string;
  finishTime?: number;
  errorMsg: string;
  datas: VideoStatusInfo[];
}

export interface GenerateVideoGifRequest {
  uuid: string;
  requestId: string;
}

export interface GenerateVideoGifResponse {
  success: boolean;
  status: 'pending' | 'processing' | 'finish' | 'failed' | 'completed';
  gifUrl: string;
  errorMsg: string;
}

export interface ListVideoResponse {
  hasNext: boolean;
  nextUuid: string;
  videoInfos: string;
}

export interface MergeVideoRequest {
  uuid: string;
  requestIds: string[];
  addMark: boolean;
}

export interface VideoModifiedResponse {
  success: boolean;
  taskId: string;
  errorMsg: string;
}

export interface MarkVideoRequest {
  uuid: string;
  requestId: string;
}

export interface MarkMergeVideoRequest {
  uuid: string;
  url: string;
}

export interface GenerateOpenUrlRequest {
  fileName: string;
}

export interface GenerateOpenUrlResponse {
  success: boolean;
  url: string;
  errorMsg: string;
}

export interface CheckMergeVideoStatusRequest {
  taskId: string;
  uuid: string;
}

export interface CheckMergeVideoStatusResponse {
  success: boolean;
  url?: string;
  status: 'pending' | 'processing' | 'finish' | 'failed';
  videoKey?: string;
  errorMsg?: string;
}

export interface CheckMarkVideoStatusRequest {
  taskId: string;
  uuid: string;
  requestId: string;
}

export interface CheckMarkVideoStatusResponse {
  success: boolean;
  url?: string;
  status: 'pending' | 'processing' | 'finish' | 'failed';
  videoKey?: string;
  errorMsg?: string;
}

// Generate video (生成视频)
export const generateVideo = (data: GenerateVideoRequest) => {
  return request('/r/Adaptor/MaterialRpcI/generateVideo', [data]);
};

// Check video generation status (查看视频生成状态)
export const checkVideoStatus = (data: CheckVideoStatusRequest) => {
  return request('/r/Adaptor/MaterialRpcI/checkVideoStatus', [data]);
};

// Generate GIF (生成GIF)
export const generateGif = (data: GenerateGifRequest) => {
  return request('/r/Adaptor/MaterialRpcI/generateGif', [data]);
};

// Check GIF generation status (轮询GIF的生成结果)
export const checkGifStatus = (data: CheckGifStatusRequest): Promise<GenerateVideoGifResponse> => {
  return request('/r/Adaptor/MaterialRpcI/checkGifStatus', [data]);
};

// Regenerate video (重新生成视频)
export const reGenerateVideo = (data: ReGenerateVideoRequest) => {
  return request('/r/Adaptor/MaterialRpcI/reGenerateVideo', [data]);
};

// Get video list (获取视频列表)
export const listVideos = (data: ListVideosRequest) => {
  return request('/r/Adaptor/MaterialRpcI/listVideos', [data]);
};

// Rate video (评价视频√)
export const rateVideo = (data: RateVideoRequest) => {
  return request('/r/Adaptor/MaterialRpcI/rateVideo', [data]);
};

// Upload image (上传图片√)
export const uploadImage = (data: UploadImageRequest) => {
  return request('/r/Adaptor/FileRpcI/upload', [data]);
};

// Delete video (删除视频√)
export const removeVideo = (data: RemoveVideoRequest) => {
  return request('/r/Adaptor/MaterialRpcI/removeVideo', [data]);
};

// Generate multiple videos (生成多个视频√)
export const generateMultiVideo = (data: { datas: GenerateMultiVideoRequest[] }): Promise<GenerateMultiVideoResponse> => {
  return request('/r/Adaptor/MaterialRpcI/generateMultiVideo', [data]);
};

// Check multiple videos status (检查多个视频状态√)
export const checkMultiVideoStatus = (data: CheckVideoStatusRequest): Promise<CheckMultiVideoStatusResponse> => {
  return request('/r/Adaptor/MaterialRpcI/checkMultiVideoStatus', [data]);
};

// Generate GIF by request ID (根据请求ID生成GIF)
export const generateGifByRequestId = (data: GenerateVideoGifRequest): Promise<GenerateVideoGifResponse> => {
  return request('/r/Adaptor/MaterialRpcI/generateGifByRequestId', [data]);
};

// Check GIF status by request ID (根据请求ID检查GIF状态)
export const checkGifStatusByRequestId = (data: GenerateVideoGifRequest): Promise<GenerateVideoGifResponse> => {
  return request('/r/Adaptor/MaterialRpcI/checkGifStatusByRequestId', [data]);
};

// List multiple videos (列出多个视频√)
export const listMultiVideos = (data: ListVideosRequest): Promise<ListVideoResponse> => {
  return request('/r/Adaptor/MaterialRpcI/listMultiVideos', [data]);
};

// Merge videos (合并视频)
export const mergeVideo = (data: MergeVideoRequest): Promise<VideoModifiedResponse> => {
  return request('/r/Adaptor/MaterialRpcI/mergeVideo', [data]);
};

// Check merge video status (检查合并视频状态)
export const checkMergeVideoStatus = (data: CheckMergeVideoStatusRequest): Promise<CheckMergeVideoStatusResponse> => {
  return request('/r/Adaptor/MaterialRpcI/checkMergeVideoStatus', [data]);
};

// Mark video (视频水印)
export const markVideo = (data: MarkVideoRequest): Promise<VideoModifiedResponse> => {
  return request('/r/Adaptor/MaterialRpcI/markVideo', [data]);
};

// Mark merged video (合成视频水印)
export const markMergedVideo = (data: MarkMergeVideoRequest): Promise<VideoModifiedResponse> => {
  return request('/r/Adaptor/MaterialRpcI/markMergedVideo', [data]);
}

// Check mark video status (检查视频水印状态)
export const checkMarkVideoStatus = (data: CheckMarkVideoStatusRequest): Promise<CheckMarkVideoStatusResponse> => {
  return request('/r/Adaptor/MaterialRpcI/checkMarkVideoStatus', [data]);
};

// Generate open URL (生成打开URL)
export const generateOpenUrl = (data: GenerateOpenUrlRequest): Promise<GenerateOpenUrlResponse> => {
  return request('/r/Adaptor/MaterialRpcI/generateOpenUrl', [data]);
};
