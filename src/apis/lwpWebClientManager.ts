import { LwpWebClient, SignerConfig } from '@/lwp-client';
import { getCookie } from '@/utils/util';
import rawSignerConfig from '../../config/signer.json';

// LwpWebClient manager for browser environment
export class LwpWebClientManager {
  private static instance: LwpWebClientManager;

  static getInstance(): LwpWebClientManager {
    if (!LwpWebClientManager.instance) {
      LwpWebClientManager.instance = new LwpWebClientManager();
    }
    return LwpWebClientManager.instance;
  }

  private client: LwpWebClient | null = null;

  private isConnected = false;

  private isConnecting = false;

  private constructor() {
    // Private constructor for singleton pattern
  }

  // Check if connected
  isClientConnected(): boolean {
    return this.isConnected;
  }

  // Disconnect client
  disconnect(): void {
    this.isConnected = false;
    this.client = null;
  }

  // Send LWP request
  async sendRequest<T = any>(
    uri: string,
    body: any[] = [],
    headers: { [key: string]: string } = {},
    timeout?: number,
  ): Promise<T> {
    await this.ensureConnection();

    if (!this.client || !this.isConnected) {
      throw new Error('LwpWebClient is not connected');
    }

    const response = await this.client.sendMsg(
      {
        lwp: uri,
        headers,
        body,
      },
      timeout ? { timeout } : undefined,
    );

    // Handle response similar to original base.ts logic
    const responseOK = response.code === 200;

    if (responseOK) {
      return response.body as T;
    }

    // Throw error for non-200 responses
    throw new Error(response.body?.reason || `Request failed with code: ${response.code}`);
  }

  // Ensure connection is established
  private async ensureConnection(): Promise<void> {
    if (this.isConnected) {
      return;
    }

    if (this.isConnecting) {
      // Wait for ongoing connection attempt
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.isConnected) {
            resolve();
          } else if (!this.isConnecting) {
            reject(new Error('Connection failed'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    this.isConnecting = true;

    this.initClient();

    if (!this.client) {
      this.isConnecting = false;
      throw new Error('Failed to initialize LwpWebClient');
    }

    // Connect with error handling
    await this.client.connect((isLoginStatusValid: boolean, isKickout: boolean) => {
      this.isConnected = false;

      // Log different disconnect reasons for debugging
      if (isLoginStatusValid) {
        console.warn('LWP connection lost due to network issues');
      } else if (isKickout) {
        console.warn('LWP connection kicked out due to multiple login');
      } else {
        console.warn('LWP connection lost due to session expiration');
      }
    });

    this.isConnected = true;
    this.isConnecting = false;
  }

  // Get WebSocket URL based on current environment
  private getWsUrl(): string {
    if (/pre-/.test(window.location.hostname)) {
      return 'wss://webalfa-sandbox.dingtalk.com/long'; // Pre-release environment
    } else if (/localhost/.test(window.location.hostname)) {
      return 'ws://ws-daily.dingtalk.com:7001/long'; // Daily environment
    } else {
      return 'wss://webalfa-cm3.dingtalk.com/long'; // Production environment
    }
  }

  // Get appKey based on current domain
  private getAppKey(): string {
    const hostname = window.location.hostname;

    if (hostname.includes('7ding.ai')) {
      return '65524d92519a39a08d5cff1f266b23d9'; // 7ding.ai domain appKey
    } else if (hostname.includes('dingtalk.com')) {
      return '5b46698304b45807569d343fcc5a2b61'; // dingtalk.com domain appKey
    } else {
      // Default fallback to dingtalk.com appKey
      return '5b46698304b45807569d343fcc5a2b61';
    }
  }

  // Initialize LwpWebClient
  private initClient(): void {
    if (!this.client) {
      // Convert raw JSON config to properly typed SignerConfig
      const signerConfig: SignerConfig = {
        ...rawSignerConfig,
        hashAlg1: rawSignerConfig.hashAlg1 as any, // Type assertion for HashAlgorithm
        hashAlg2: rawSignerConfig.hashAlg2 as any,
        hashAlg3: rawSignerConfig.hashAlg3 as any,
        // Type assertion for concatenation order
        concatenationOrder: rawSignerConfig.concatenationOrder as any,
      };

      this.client = new LwpWebClient({
        wsUrl: this.getWsUrl(),
        appKey: this.getAppKey(), // Dynamic appKey based on domain
        lang: 'zh-CN', // Language setting
        regType: '1', // Secondary connection type
        aesEncryption: false, // Can be set to true if needed
        signerConfig,
        getRegAuthInfo: async () => {
          // Get authentication info from cookies
          const token = getCookie('account');
          const deviceId = getCookie('deviceid');
          return {
            token,
            deviceId,
          };
        },
      });
    }
  }
}

// Export singleton instance
export const lwpWebClientManager = LwpWebClientManager.getInstance();
