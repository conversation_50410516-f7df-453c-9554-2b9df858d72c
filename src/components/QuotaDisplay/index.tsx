import React from 'react';
import { QuotaUsageResponse } from '@/apis/quota';
import './index.less';

interface QuotaDisplayProps {
  quotaUsage: QuotaUsageResponse | null;
  loading?: boolean;
  className?: string;
  placeholder?: string;
  showPlaceholder?: boolean;
}

/**
 * 额度显示组件
 * 解决加载时文字宽度闪动的问题，通过固定宽度和占位符来保持布局稳定
 */
const QuotaDisplay: React.FC<QuotaDisplayProps> = ({
  quotaUsage,
  loading = false,
  className = '',
  placeholder = '--',
  showPlaceholder = true,
}) => {
  // 格式化显示文本
  const formatQuotaText = (): string => {
    if (loading || !quotaUsage) {
      return showPlaceholder ? placeholder : '';
    }
    return `${(quotaUsage.totalQuota || 0) - (quotaUsage.usedQuota || 0)}`;
  };

  return (
    <span className={`quota-display ${className} ${loading || !quotaUsage ? 'quota-loading' : ''}`}>
      {formatQuotaText()}
    </span>
  );
};

export default QuotaDisplay;
