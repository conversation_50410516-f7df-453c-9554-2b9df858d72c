@transition-easy: cubic-bezier(0.23, 1, 0.32, 1);
@transition-duration: 0.3s;

.yida-m-image-viewer {
  // 组件样式覆盖
  .PhotoView-PhotoSlider__BannerWrap {
    box-sizing: border-box;
    padding: 4px 4px 0;
    top: env(safe-area-inset-top);
    background-color: transparent;
  }
  .PhotoView-PhotoSlider__Counter {
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #f5f2f0;
    border-radius: 4px;
    opacity: 1;
  }
  .PhotoView-PhotoSlider__BannerRight {
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
  }

  .viewer-img {
    background-color: #fff;
    user-select: none; // 不可选中
  }

  .svg-icon {
    fill: currentColor;
  }

  .next-icon {
    margin-top: -2px; // 图标略微向下偏移了
  }

  .viewer-actions {
    box-sizing: border-box;
    position: absolute;
    left: 50%;
    bottom: 60px;
    transform: translateX(-50%);
    border: 1px solid rgba(255, 255, 255, 0.6);
    border-radius: 20px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 20;

    .actions {
      display: flex;
    }

    .action {
      padding: 7px 0;
      cursor: pointer;

      &:hover {
        background-color: rgba(255, 255, 255, 0.4);
      }

      &:first-child {
        border-top-left-radius: 20px;
        border-bottom-left-radius: 20px;
        .action-content {
          border-left-width: 0;
        }
      }

      &:last-child {
        border-top-right-radius: 20px;
        border-bottom-right-radius: 20px;
      }

      &-content {
        display: flex;
        align-items: center;
        padding: 0 14px;
        height: 24px;
        border-left: 1px solid rgba(255, 255, 255, 0.6);
      }
    }
  }

  .viewer-actions--mobile {
    position: absolute;
    right: 4.267vw;
    bottom: 48px;
    color: #fff;
    z-index: 20;

    .actions {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .action {
      display: flex;
      font-size: 18px;
      width: 40px;
      height: 40px;
      align-items: center;
      justify-content: center;
      margin-top: 32px;
      background-color: rgba(153, 153, 153, 0.4);
      border-radius: 50%;
    }
  }
}

.yida-m-image-viewer--photo {
  position: relative;
  overflow: hidden;

  .svg-icon {
    fill: currentColor;
  }

  .photo-item {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform @transition-easy @transition-duration;

    & > img {
      position: absolute;
      margin: auto;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      max-width: 100%;
      max-height: 100%;
    }
  }

  .photo-before {
    display: flex;
    align-items: center;
    justify-content: center;

    position: absolute;
    left: 50%;
    top: 50%;
    margin: -16px 0 0 -16px;
    width: 32px;
    height: 32px;
    font-size: 16px;
    background-color: rgba(51, 51, 51, 0.6);
    border-radius: 16px 16px;
    z-index: 2;
    color: #fff;
    cursor: pointer;
    opacity: 0;
    transform: scale(1.2);
    transition-property: transform, opacity;
    transition-duration: @transition-duration;
    transition-timing-function: @transition-easy;
    transition-delay: 0s;
  }

  &:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);
    z-index: 1;
    transition-property: background-color;
    transition-duration: @transition-duration;
    transition-timing-function: @transition-easy;
    transition-delay: 0s;
  }

  &:hover {
    .photo-item {
      transform: scale(1.1);
    }

    .photo-before {
      opacity: 1;
      transform: scale(1);
    }

    &:after {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
}
