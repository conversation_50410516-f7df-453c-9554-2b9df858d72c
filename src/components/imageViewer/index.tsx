import * as React from 'react';
import { createRoot } from 'react-dom/client';
import { PhotoSlider } from 'react-photo-view';
import 'react-photo-view/dist/index.css';

import Icon from './Icon';
import Photo from './Photo';
import './index.less';

interface ImgData {
  src: string;
  [propName: string]: any;
}

interface Props {
  isMobileEnv?: boolean;
  urlLists?: ImgData[];
  iconSize?: number;
  defaultIndex?: number;
  onDownload?: (index: number, urlLists: ImgData[]) => void;
  visible?: boolean;
  onClose?: () => void;
  [propName: string]: any;
}

interface ShowOptions extends Props {
  photos?: { data: Array<{ src: string }> } | Array<{ src: string }>;
  current?: number;
}

interface PreviewOptions extends ShowOptions {
  width: string;
  height: string;
  disableView?: boolean;
}

interface CustomRender {
  rotate: number;
  onRotate: (value: number) => void;
  scale: number;
  onScale: (value: number) => void;
  index: number;
}

function ImageViewer(props: Props) {
  const { isMobileEnv, urlLists, iconSize, defaultIndex, onDownload, ...rest } = props;
  const [currentIndex, setCurrentIndex] = React.useState(defaultIndex || 0);

  // 自定义弹窗内容定义
  const overlayRender = isMobileEnv
    ? ({ rotate, onRotate, index }: CustomRender) => {
      const size = iconSize || 18;
      return (
        <div className="viewer-actions--mobile">
          <div className="actions">
            <div className="action" onClick={() => onRotate(rotate - 90)}>
              <Icon size={size} type="rotate-left" />
            </div>
            <div className="action" onClick={() => onRotate(rotate + 90)}>
              <Icon size={size} type="rotate-right" />
            </div>
            {onDownload && (
              <div className="action" onClick={() => onDownload(index, urlLists)}>
                <Icon type="download" />
              </div>
            )}
          </div>
        </div>
      );
    }
    : ({ rotate, onRotate, scale, onScale, index }: CustomRender) => {
      const size = iconSize || 16;
      return (
        <div className="viewer-actions">
          <div className="actions">
            <div className="action" onClick={() => onScale(scale + 0.2)}>
              <div className="action-content">
                <Icon type="zoom-in" size={size} />
              </div>
            </div>
            <div className="action" onClick={() => onScale(scale - 0.2)}>
              <div className="action-content">
                <Icon type="zoom-out" size={size} />
              </div>
            </div>
            <div className="action" onClick={() => onRotate(rotate - 90)}>
              <div className="action-content">
                <Icon type="rotate-left" size={size} />
              </div>
            </div>
            <div className="action" onClick={() => onRotate(rotate + 90)}>
              <div className="action-content">
                <Icon type="rotate-right" size={size} />
              </div>
            </div>
            {onDownload && (
              <div className="action" onClick={() => onDownload(index, urlLists)}>
                <div className="action-content">
                  <Icon type="download" size={size} />
                </div>
              </div>
            )}
          </div>
        </div>
      );
    };

  const imageList = (urlLists || []).map((item) => ({ src: item.src }));

  return (
    <PhotoSlider
      images={imageList}
      className="yida-m-image-viewer"
      imageClassName="viewer-img"
      overlayRender={overlayRender}
      index={currentIndex}
      onIndexChange={setCurrentIndex}
      {...rest}
    />
  );
}

let globalOptions: ShowOptions = {};
ImageViewer.setGlobal = (options: ShowOptions) => {
  globalOptions = options;
};

ImageViewer.getGlobal = () => {
  return {
    ...globalOptions,
  };
};

let container: any;
let root: any;
ImageViewer.show = (options: ShowOptions) => {
  if (!container) {
    container = document.createElement('div');
    container.className = 'yida-m-image-viewer--wrapper';
    document.body.appendChild(container);
    root = createRoot(container);
  }

  const { photos, current, ...rest } = {
    ...globalOptions,
    ...(options || {}),
  };
  const urlLists: ImgData[] = photos?.data || photos || [];
  root.render(
    <ImageViewer
      urlLists={urlLists}
      defaultIndex={current}
      {...rest}
      visible
      onClose={() => {
        root.unmount();
      }}
    />,
  );
};

ImageViewer.Photo = Photo;

/**
 * @description 照片查看
 */
function PhotoPreview(props: PreviewOptions) {
  const { photos, current, width, height, disableView, ...rest } = {
    ...globalOptions,
    ...(props || {}),
  };

  const urlLists: ImgData[] = photos?.data || photos || [];
  const imageList = (urlLists || []).map((item) => ({ src: item.src }));
  const currentItem = imageList[current] || imageList[0] || {};

  const [visible, setVisible] = React.useState(false);

  return (
    <div>
      <Photo
        width={width || '400px'}
        height={height || '200px'}
        disableView={disableView}
        imageUrl={currentItem.src}
        onClick={() => setVisible(true)}
      />
      <ImageViewer
        urlLists={urlLists}
        defaultIndex={current}
        {...rest}
        visible={visible}
        onClose={() => setVisible(false)}
      />
    </div>
  );
}

ImageViewer.PhotoPreview = PhotoPreview;

export default ImageViewer;
