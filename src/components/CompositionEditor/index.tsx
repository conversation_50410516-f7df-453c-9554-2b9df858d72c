import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Canvas, FabricImage } from 'fabric';
import { Button, Toast } from 'dingtalk-design-mobile';
import {
  CloseOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  DownLOutlined,
  RefreshOutlined
} from '@ali/ding-icons';
import { i18next } from '@ali/dingtalk-i18n';
import { CompositionEditorProps, BackgroundSize } from './types';
import useImageUpload from '@/hooks/useImageUpload';
import './index.less';

const BACKGROUND_SIZES: BackgroundSize[] = [
  { id: '1:1', name: '1:1', width: 800, height: 800, ratio: '1:1' },
  { id: '3:4', name: '3:4', width: 750, height: 1000, ratio: '3:4' },
  { id: '16:9', name: '16:9', width: 1280, height: 720, ratio: '16:9' }
];

const SELECTION_STYLES = {
  padding: 0,
  borderScaleFactor: 2,
  cornerSize: 12,
  cornerColor: '#FF0E53',
  cornerStyle: 'circle' as const,
  transparentCorners: false,
  borderColor: '#FF0E53',
  borderOpacityWhenMoving: 1,
  borderDashArray: null,
};

const CANVAS_CONFIG = {
  backgroundColor: 'transparent',
  selection: true,
  preserveObjectStacking: true,
};

const CANVAS_SELECTION_CONFIG = {
  selectionColor: 'rgba(255, 14, 83, 0.1)',
  selectionBorderColor: '#FF0E53',
  selectionLineWidth: 2,
};

const CompositionEditor: React.FC<CompositionEditorProps> = ({
  visible,
  onClose,
  imageUrl,
  onSave
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<Canvas | null>(null);
  const [selectedSize, setSelectedSize] = useState<BackgroundSize>(BACKGROUND_SIZES[0]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const [initialImageState, setInitialImageState] = useState<{
    angle: number;
    scaleX: number;
    scaleY: number;
    left: number;
    top: number;
  } | null>(null);

  const { uploadImage, loading: uploadLoading } = useImageUpload();

  const loadMainImage = useCallback(() => {
    if (!fabricCanvasRef.current || !imageUrl) return;

    const canvas = fabricCanvasRef.current;
    canvas.getObjects().forEach((obj) => canvas.remove(obj));

    FabricImage.fromURL(imageUrl, { crossOrigin: 'anonymous' })
      .then((img) => {
        if (img && fabricCanvasRef.current) {
          const canvas = fabricCanvasRef.current;
          const canvasWidth = canvas.getWidth();
          const canvasHeight = canvas.getHeight();
          const centerX = canvasWidth / 2;
          const centerY = canvasHeight / 2;
          
          const targetWidth = canvasWidth * 0.5;
          const imageAspectRatio = img.width! / img.height!;
          const targetHeight = targetWidth / imageAspectRatio;
          
          const scaleX = targetWidth / img.width!;
          const scaleY = targetHeight / img.height!;
          
          img.set({
            left: centerX,
            top: centerY,
            scaleX,
            scaleY,
            selectable: true,
            evented: true,
            originX: 'center',
            originY: 'center',
            ...SELECTION_STYLES,
          });

          canvas.add(img);
          canvas.setActiveObject(img);
          canvas.renderAll();

          setInitialImageState({
            angle: 0,
            scaleX,
            scaleY,
            left: centerX,
            top: centerY,
          });
        }
      })
      .catch((error) => {
        console.error('Failed to load image:', error);
      });
  }, [imageUrl]);

  const calculateCanvasSize = useCallback(() => {
    const headerHeight = 60;
    const toolbarHeight = 60;
    const actionsHeight = 80;
    const padding = 40;

    const availableWidth = window.innerWidth;
    const availableHeight = window.innerHeight - headerHeight - toolbarHeight - actionsHeight - padding;
    const ratio = selectedSize.width / selectedSize.height;

    let canvasWidth = availableWidth;
    let canvasHeight = canvasWidth / ratio;

    if (canvasHeight > availableHeight) {
      canvasHeight = availableHeight;
      canvasWidth = canvasHeight * ratio;
    }

    const minSize = 200;
    if (canvasWidth < minSize) {
      canvasWidth = minSize;
      canvasHeight = canvasWidth / ratio;
    }
    if (canvasHeight < minSize) {
      canvasHeight = minSize;
      canvasWidth = canvasHeight * ratio;
    }
    
    return { width: canvasWidth, height: canvasHeight };
  }, [selectedSize]);

  const handleRotate = useCallback((direction: 'left' | 'right') => {
    const canvas = fabricCanvasRef.current;
    const activeObject = canvas?.getActiveObject();
    if (activeObject) {
      const angle = direction === 'right' ? -90 : 90;
      activeObject.rotate((activeObject.angle || 0) + angle);

      canvas.discardActiveObject();
      canvas.setActiveObject(activeObject);
      activeObject.set(SELECTION_STYLES);
      canvas.renderAll();
    }
  }, []);

  const handleZoom = useCallback((direction: 'in' | 'out') => {
    const canvas = fabricCanvasRef.current;
    const activeObject = canvas?.getActiveObject();
    if (activeObject) {
      const scale = direction === 'out' ? 1.1 : 0.9;
      activeObject.scaleX = (activeObject.scaleX || 1) * scale;
      activeObject.scaleY = (activeObject.scaleY || 1) * scale;

      canvas.discardActiveObject();
      canvas.setActiveObject(activeObject);
      activeObject.set(SELECTION_STYLES);
      canvas.renderAll();
    }
  }, []);

  const handleSizeChange = useCallback((size: BackgroundSize) => {
    setSelectedSize(size);
  }, []);

  const handleReset = useCallback(() => {
    if (!fabricCanvasRef.current || !initialImageState) return;

    const canvas = fabricCanvasRef.current;
    const activeObject = canvas.getActiveObject();
    
    if (activeObject) {
      canvas.discardActiveObject();
      canvas.renderAll();
      
      activeObject.set({
        angle: initialImageState.angle,
        scaleX: initialImageState.scaleX,
        scaleY: initialImageState.scaleY,
        left: initialImageState.left,
        top: initialImageState.top,
        ...SELECTION_STYLES,
      });

      canvas.setActiveObject(activeObject);
      canvas.renderAll();

      Toast.success({
        content: i18next.t('j-dingtalk-web_components_CompositionEditor_ResetSuccess'),
        duration: 2,
      });
    }
  }, [initialImageState]);

  const handleExport = useCallback(async () => {
    if (!fabricCanvasRef.current) return;

    setIsLoading(true);
    try {
      const canvas = fabricCanvasRef.current;

      // Ensure canvas has content
      const objects = canvas.getObjects();
      if (objects.length === 0) {
        throw new Error('Canvas is empty');
      }

      const dataURL = canvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 2, // Higher resolution
        enableRetinaScaling: true,
      });

      // Validate exported data URL
      if (!dataURL || dataURL === 'data:,') {
        throw new Error('Failed to export canvas data');
      }

      const filename = `composition_${selectedSize.ratio}_${Date.now()}.png`;

      const sizeConstraints = {
        minWidth: selectedSize.width,
        minHeight: selectedSize.height,
        maxWidth: selectedSize.width,
        maxHeight: selectedSize.height,
      };

      const imageUrl = await uploadImage(dataURL, filename, sizeConstraints);

      if (imageUrl) {
        onSave(imageUrl);
        Toast.success({
          content: i18next.t('j-dingtalk-web_components_CompositionEditor_ExportSuccess'),
          duration: 2,
        });
      } else {
        // Fallback: save as base64 data URL
        console.warn('Upload failed, falling back to base64 data URL');
        onSave(dataURL);
        Toast.info({
          content: i18next.t('j-dingtalk-web_components_CompositionEditor_ExportSuccess'),
          duration: 2,
        });
      }
    } catch (error) {
      console.error('Export error:', error);
      Toast.fail({
        content: i18next.t('j-dingtalk-web_components_CompositionEditor_ExportFailed'),
        duration: 2,
      });
    } finally {
      setIsLoading(false);
    }
  }, [onSave, uploadImage, selectedSize.ratio]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  useEffect(() => {
    if (visible) {
      setShouldRender(true);
      const { scrollY } = window;

      document.body.classList.add('composition-editor-open');
      document.body.style.top = `-${scrollY}px`;

      requestAnimationFrame(() => {
        setIsAnimating(true);
      });

      return () => {
        document.body.classList.remove('composition-editor-open');
        document.body.style.top = '';
        window.scrollTo(0, scrollY);
      };
    } else if (shouldRender) {
      setIsAnimating(false);
      const timer = setTimeout(() => {
        setShouldRender(false);
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [visible, shouldRender]);

  useEffect(() => {
    if (visible) {
      const initializeCanvas = () => {
        if (fabricCanvasRef.current) {
          fabricCanvasRef.current.dispose();
        }

        const { width, height } = calculateCanvasSize();

        try {
          const canvas = new Canvas(canvasRef.current, {
            width,
            height,
            ...CANVAS_CONFIG,
          });

          const applySelectionStyles = (activeObject: any) => {
            activeObject.set(SELECTION_STYLES);
            canvas.renderAll();
          };

          canvas.on('selection:created', () => {
            const activeObject = canvas.getActiveObject();
            if (activeObject) applySelectionStyles(activeObject);
          });

          canvas.on('selection:updated', () => {
            const activeObject = canvas.getActiveObject();
            if (activeObject) applySelectionStyles(activeObject);
          });

          fabricCanvasRef.current = canvas;
          
          Object.assign(canvas, CANVAS_SELECTION_CONFIG);
          
          if (imageUrl) {
            setTimeout(() => loadMainImage(), 50);
          }
        } catch (error) {
          console.error('Error creating canvas:', error);
        }
      };
      
      const timer = setTimeout(() => {
        if (!canvasRef.current) {
          const retryTimer = setTimeout(() => {
            if (canvasRef.current) initializeCanvas();
          }, 100);
          return () => clearTimeout(retryTimer);
        }
        initializeCanvas();
      }, 200);
      
      return () => clearTimeout(timer);
    }
  }, [visible, calculateCanvasSize, imageUrl, loadMainImage]);

  useEffect(() => {
    if (visible && fabricCanvasRef.current && imageUrl) {
      const objects = fabricCanvasRef.current.getObjects();
      if (objects.length === 0) {
        setTimeout(() => loadMainImage(), 100);
      }
    }
  }, [visible, imageUrl, loadMainImage]);

  useEffect(() => {
    if (visible && fabricCanvasRef.current) {
      const { width, height } = calculateCanvasSize();
      fabricCanvasRef.current.setDimensions({ width, height });
      fabricCanvasRef.current.renderAll();
    }
  }, [selectedSize, visible, calculateCanvasSize]);

  useEffect(() => {
    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
    };
  }, []);

  const toolbarButtons = useMemo(() => (
    <>
      <Button size="small" onClick={() => handleRotate('left')} icon={<RotateLeftOutlined />} />
      <Button size="small" onClick={() => handleRotate('right')} icon={<RotateRightOutlined />} />
      <Button size="small" onClick={() => handleZoom('out')} icon={<ZoomOutOutlined />} />
      <Button size="small" onClick={() => handleZoom('in')} icon={<ZoomInOutlined />} />
      {BACKGROUND_SIZES.map((size) => (
        <Button
          key={size.id}
          size="small"
          type={selectedSize.id === size.id ? 'primary' : 'secondary'}
          onClick={() => handleSizeChange(size)}
        >
          {size.name}
        </Button>
      ))}
    </>
  ), [selectedSize.id, handleRotate, handleZoom, handleSizeChange]);

  const actionButtons = useMemo(() => (
    <>
      <Button onClick={onClose}>
        {i18next.t('j-dingtalk-web_components_CompositionEditor_Cancel')}
      </Button>
      <Button
        onClick={handleReset}
        disabled={!initialImageState}
        icon={<RefreshOutlined />}
      >
        {i18next.t('j-dingtalk-web_components_CompositionEditor_Reset')}
      </Button>
      <Button
        type="primary"
        loading={isLoading || uploadLoading}
        onClick={handleExport}
        icon={<DownLOutlined />}
      >
        {isLoading || uploadLoading
          ? i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading')
          : i18next.t('j-dingtalk-web_components_CompositionEditor_Export')}
      </Button>
    </>
  ), [onClose, handleReset, initialImageState, isLoading, uploadLoading, handleExport]);

  if (!shouldRender) {
    return null;
  }

  const modalContent = (
    <div
      className={`composition-editor-overlay ${isAnimating ? 'animate-in' : 'animate-out'}`}
      onClick={handleBackdropClick}
    >
      <div className="composition-editor-content" onClick={handleContentClick}>
        <div className="editor-header">
          <h2 className="editor-title">
            {i18next.t('j-dingtalk-web_components_CompositionEditor_Title')}
          </h2>
          <button className="close-button" onClick={onClose}>
            <CloseOutlined />
          </button>
        </div>

        <div className="editor-toolbar">
          <div className="toolbar-buttons">
            {toolbarButtons}
          </div>
        </div>

        <div className="canvas-container">
          <canvas ref={canvasRef} />
        </div>

        <div className="editor-actions">
          {actionButtons}
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default CompositionEditor;
