// Composition Editor Overlay
.composition-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  z-index: 1000;
  transition: opacity 0.2s ease;

  &.animate-in {
    opacity: 1;
  }

  &.animate-out {
    opacity: 0;
  }

  .close-button {
    // position: absolute;
    // top: 20px;
    // right: 20px;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    z-index: 1001;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

// Composition Editor Content
.composition-editor-content {
  border-radius: 12px;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

  .editor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px 0;
    border-bottom: 1px solid #e8e8e8;

    .editor-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .editor-toolbar {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;

    .toolbar-buttons {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
      justify-content: center;
      max-width: 100%;

      // Group related buttons with visual separation
      .ding-button:not(:last-child) {
        margin-right: 0;
      }

      // Add visual separator between button groups using pseudo-elements
      .ding-button:nth-child(4) {
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          right: -12px;
          top: 50%;
          transform: translateY(-50%);
          width: 1px;
          height: 20px;
          background: #d9d9d9;
        }
      }

      .ding-button:nth-child(6) {
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          right: -12px;
          top: 50%;
          transform: translateY(-50%);
          width: 1px;
          height: 20px;
          background: #d9d9d9;
        }
      }
    }
  }

  .canvas-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    overflow: auto;
    background: transparent;
    min-height: 0; // Allow flex item to shrink

    canvas {
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      background: transparent;
      background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAPUlEQVR4AeySywkAMAhDH52h+0/YIRoH8IMnD0JyCgSe5gA3sWJfVuCnhWQLYMYNnr4VOdzJDAQR9LUI8AEAAP//ViLpiAAAAAZJREFUAwBk7gjBheCOvgAAAABJRU5ErkJggg==');
      background-repeat: repeat;
      background-size: 20px 20px;
      width: 100%;
      height: 100%;
    }
  }

  .editor-actions {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e8e8e8;

    .ding-button {
      flex: 1;
      max-width: 120px;
    }
  }
}

// Body scroll lock
.composition-editor-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
}

// Responsive design
@media (max-width: 768px) {
  .composition-editor-content {
    width: 100vw;
    height: 100vh;
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    border-radius: 0;

    .editor-header {
      padding: 12px 16px 0;
    }

    .editor-toolbar {
      padding: 12px 16px;

      .toolbar-buttons {
        gap: 6px;
        flex-wrap: wrap;
        justify-content: center;

        // Remove separators on mobile for cleaner look
        .ding-button:nth-child(4)::after,
        .ding-button:nth-child(6)::after {
          display: none;
        }
      }
    }

    .canvas-container {
      padding: 0;
      min-height: 0;

      canvas {
        width: 100%;
        height: 100%;
        border-radius: 0;
      }
    }

    .editor-actions {
      padding: 16px;
      gap: 8px;

      .ding-button {
        flex: 1;
        max-width: none;
        font-size: 14px;
      }
    }
  }

  .composition-editor-overlay {
    .close-button {
      // top: calc(env(safe-area-inset-top) + 10px);
      // right: 10px;
      width: 24px;
      height: 24px;

      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
}