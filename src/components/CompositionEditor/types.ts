// Composition Editor Types
export interface BackgroundSize {
  id: string;
  name: string;
  width: number;
  height: number;
  ratio: string;
}

export interface CompositionEditorProps {
  visible: boolean;
  onClose: () => void;
  imageUrl: string;
  onSave: (imageDataUrl: string) => void;
}

export interface CanvasConfig {
  width: number;
  height: number;
  backgroundColor: string;
}

export interface ImageTransform {
  left: number;
  top: number;
  scaleX: number;
  scaleY: number;
  angle: number;
}
