import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import './index.less';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  fit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  lazy?: boolean;
  progressive?: boolean;
  quality?: number;
  format?: 'webp' | 'jpg' | 'png';
  placeholder?: string;
  referrerPolicy?: 'no-referrer' | 'no-referrer-when-downgrade' | 'origin' | 'origin-when-cross-origin' | 'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url';
  onLoad?: () => void;
  onError?: () => void;
  onClick?: () => void;
}

// Check if URL is an Alibaba Cloud CDN
const isAlibabaCloudCDN = (url: string): boolean => {
  return url.includes('img.alicdn.com') || url.includes('gw.alicdn.com');
};

// Image URL optimization utility
const optimizeImageUrl = (
  url: string,
  options: {
    width?: number | string;
    height?: number | string;
    quality?: number;
    format?: string;
  } = {},
) => {
  if (!url || typeof url !== 'string') return url;

  const { width, height, quality = 80, format = 'webp' } = options;

  // For Alibaba CDN images - check for exact domain match to avoid false positives
  if (isAlibabaCloudCDN(url)) {
    const params = [];
    if (width) params.push(`w_${width}`);
    if (height) params.push(`h_${height}`);
    params.push(`q_${quality}`);
    if (format) params.push(`f_${format}`);

    // Add image processing parameters
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}x-oss-process=image/resize,m_fill,${params.join(',')}`;
  }

  return url;
};

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  fit = 'cover',
  lazy = true,
  progressive = false,
  quality = 80,
  format = 'webp',
  placeholder,
  referrerPolicy = 'no-referrer',
  onLoad,
  onError,
  onClick,
}) => {
  const [imageState, setImageState] = useState({
    loaded: false,
    error: false,
    retryCount: 0,
  });
  const [currentReferrerPolicy, setCurrentReferrerPolicy] = useState(referrerPolicy);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Referrer policies to try in case of CDN access control issues
  const referrerPolicies = useMemo<Array<'no-referrer' | 'origin' | 'no-referrer-when-downgrade'>>(
    () => ['no-referrer', 'origin', 'no-referrer-when-downgrade'],
    [],
  );

  // Optimize image URL - memoized
  const optimizedSrc = useMemo(
    () => optimizeImageUrl(src, {
      width: typeof width === 'number' ? width : undefined,
      height: typeof height === 'number' ? height : undefined,
      quality,
      format,
    }),
    [src, width, height, quality, format],
  );

  // Generate low quality placeholder for progressive loading - memoized
  const lowQualitySrc = useMemo(
    () => (progressive
      ? optimizeImageUrl(src, {
        width: typeof width === 'number' ? Math.floor(width / 10) : undefined,
        height: typeof height === 'number' ? Math.floor(height / 10) : undefined,
        quality: 20,
        format: 'jpg',
      })
      : undefined),
    [progressive, src, width, height],
  );

  // Removed IntersectionObserver - using native lazy loading instead

  // Handle retry with different referrer policies
  useEffect(() => {
    if (imageState.retryCount === 0) return;

    // Reset states for retry
    setImageState((prev) => ({ ...prev, loaded: false, error: false }));
  }, [imageState.retryCount]);

  const handleLoad = useCallback(() => {
    setImageState((prev) => ({
      ...prev,
      loaded: true,
      retryCount: 0,
    }));
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    // Try different referrer policies for CDN access control issues
    if (imageState.retryCount < referrerPolicies.length - 1 && isAlibabaCloudCDN(src)) {
      const nextPolicy = referrerPolicies[imageState.retryCount + 1];
      setCurrentReferrerPolicy(nextPolicy);
      setImageState((prev) => ({ ...prev, retryCount: prev.retryCount + 1 }));
      return;
    }

    // All retry attempts failed
    setImageState((prev) => ({ ...prev, error: true }));
    onError?.();
  }, [imageState.retryCount, referrerPolicies, src, onError]);
  // Memoize styles to avoid recreating on every render
  const containerStyle = useMemo<React.CSSProperties>(
    () => ({
      width: width || '100%',
      height: height || '100%',
      position: 'relative',
      overflow: 'hidden',
      ...style,
    }),
    [width, height, style],
  );

  const imgStyle = useMemo<React.CSSProperties>(
    () => ({
      width: '100%',
      height: '100%',
      objectFit: fit,
      transition: imageState.loaded ? 'opacity 0.3s ease-out' : 'none',
      opacity: imageState.loaded ? 1 : 0,
      display: 'block',
    }),
    [fit, imageState.loaded],
  );

  // Placeholder style with fade out transition - memoized
  const placeholderStyle = useMemo<React.CSSProperties>(
    () => ({
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      transition: 'opacity 0.3s ease-out',
      opacity: imageState.loaded ? 0 : 1,
    }),
    [imageState.loaded],
  );

  // Main image style with fade in transition - memoized
  const mainImageStyle = useMemo<React.CSSProperties>(
    () => ({
      ...imgStyle,
      position: 'absolute',
      top: 0,
      left: 0,
    }),
    [imgStyle],
  );

  // Render loading placeholder - memoized
  const renderPlaceholder = useCallback(() => {
    if (progressive && lowQualitySrc) {
      return (
        <img
          src={lowQualitySrc}
          alt={alt}
          className="low-quality-placeholder"
          style={{
            width: '100%',
            height: '100%',
            objectFit: fit,
            filter: 'blur(5px)',
            display: 'block',
          }}
        />
      );
    }

    if (placeholder) {
      return (
        <img
          src={placeholder}
          alt={alt}
          className="static-placeholder"
          style={{
            width: '100%',
            height: '100%',
            objectFit: fit,
            display: 'block',
          }}
        />
      );
    }

    return <div className="shimmer-placeholder" />;
  }, [progressive, lowQualitySrc, placeholder, alt, fit]);

  // Error state rendering
  if (imageState.error) {
    return (
      <div
        ref={containerRef}
        className={`optimized-image-error ${className}`}
        style={containerStyle}
        onClick={onClick}
      >
        <div className="error-content">
          <span>{i18next.t('j-dingtalk-web_components_OptimizedImage_PortraitFailure')}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`optimized-image-container ${className} ${imageState.loaded ? 'loaded' : ''}`}
      style={containerStyle}
      onClick={onClick}
    >
      {/* Loading placeholder - always render but fade out when loaded */}
      {!imageState.error && (
        <div className="loading-placeholder" style={placeholderStyle}>
          {renderPlaceholder()}
        </div>
      )}

      {/* Main image - positioned above placeholder */}
      <img
        ref={imgRef}
        key={`${optimizedSrc}-${currentReferrerPolicy}`}
        src={optimizedSrc}
        alt={alt}
        style={mainImageStyle}
        onLoad={handleLoad}
        onError={handleError}
        loading={lazy ? 'lazy' : 'eager'}
        decoding="async"
        referrerPolicy={currentReferrerPolicy}
      />
    </div>
  );
};

// Memoize component to prevent unnecessary re-renders
export default React.memo(OptimizedImage, (prevProps, nextProps) => {
  // Custom comparison function for React.memo
  return (
    prevProps.src === nextProps.src &&
    prevProps.alt === nextProps.alt &&
    prevProps.width === nextProps.width &&
    prevProps.height === nextProps.height &&
    prevProps.className === nextProps.className &&
    prevProps.fit === nextProps.fit &&
    prevProps.lazy === nextProps.lazy &&
    prevProps.progressive === nextProps.progressive &&
    prevProps.quality === nextProps.quality &&
    prevProps.format === nextProps.format &&
    prevProps.placeholder === nextProps.placeholder &&
    prevProps.referrerPolicy === nextProps.referrerPolicy &&
    prevProps.onClick === nextProps.onClick &&
    prevProps.onLoad === nextProps.onLoad &&
    prevProps.onError === nextProps.onError &&
    JSON.stringify(prevProps.style) === JSON.stringify(nextProps.style)
  );
});
