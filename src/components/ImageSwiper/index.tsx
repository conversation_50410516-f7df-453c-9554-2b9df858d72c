import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { Swiper } from 'dingtalk-design-mobile';
import { SearchSOutlined } from '@ali/ding-icons';
import OptimizedImage from '@/components/OptimizedImage';
import './index.less';

interface ImageSwiperProps {
  images: string[];
  productId: string;
  className?: string;
  onClick: () => void;
}

const ImageSwiper: React.FC<ImageSwiperProps> = ({ images, productId, className = '', onClick }) => {
  const items = images.map((url) => (
    <Swiper.Item key={productId}>
      <div
        className={`image-swiper-content ${className}`}
        onClick={onClick}
      >

        <OptimizedImage
          src={url}
          alt={i18next.t('j-dingtalk-web_components_ImageSwiper_ProductPortrait')}
          width="100%"
          height="100%"
          lazy
          progressive
          quality={90}
          className="swiper-image"
        />

      </div>
    </Swiper.Item>
  ));

  return (
    <div className={`image-swiper-container ${images.length === 1 ? 'single-image' : ''}`}>
      <Swiper allowTouchMove={images.length > 1}>{items}</Swiper>
      <div
        className="image-swiper-tag"
        onClick={onClick}
      >
        <div className="image-swiper-tag-icon">
          <SearchSOutlined />
        </div>
        <div className="image-swiper-tag-text">{i18next.t('j-dingtalk-web_components_ImageSwiper_LookingForSimilarProducts')}</div>
      </div>
    </div>
  );
};

export default ImageSwiper;
