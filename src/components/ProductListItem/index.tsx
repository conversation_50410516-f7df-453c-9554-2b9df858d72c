import React from 'react';
import { IProjectModel } from '@/apis/user-recommend';
import OptimizedImage from '@/components/OptimizedImage';
import { i18next } from '@ali/dingtalk-i18n';
import { openLink$ } from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import './index.less';

interface ProductListItemProps {
  product: IProjectModel;
  showHeart?: boolean;
  className?: string;
}

const ProductListItem: React.FC<ProductListItemProps> = ({
  product,
  showHeart = true,
  className = '',
}) => {
  const handleClick = () => {
    openLink$({
      url: product?.productUrl,
      enableShare: false,
    });
  };

  return (
    <div
      className={`product-list-item ${className}`}
      onClick={handleClick}
    >
      <OptimizedImage
        className="product-list-item-img"
        src={product.primaryImage}
        alt={product.title}
        width={100}
        height={100}
      />
      <div className="product-list-item-info">
        <div className="product-list-item-title">
          {product.title}
        </div>
        <div className="product-list-item-bottom">
          <div className="product-list-item-price">
            {product.price}
            <span className="product-list-item-price-unit">{i18next.t('j-dingtalk-web_pages_create-order_Currency')}</span>
          </div>
          {showHeart && (
            <img className="product-list-item-like-icon" src="https://img.alicdn.com/imgextra/i1/O1CN011cxQ6i1akb233RZrN_!!6000000003368-2-tps-41-39.png" />
          )}
        </div>
      </div>

    </div>
  );
};

export default ProductListItem;
