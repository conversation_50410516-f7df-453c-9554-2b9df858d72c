.image-uploader {
  position: relative;

  .upload-area {
    border: 1px solid #141414;
    border-radius: 16px;
    text-align: center;
    background: #141414;
    height: 202px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 188px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    outline: none; // Remove default focus outline
    position: relative;
    overflow: hidden;

    .upload-icon {
      font-size: 48px;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 16px;
    }

    .upload-text {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.5);
      font-weight: normal;
    }

    &:active {
      border: 1px solid #FF0E53;
    }

    // Focus state for accessibility and auto-focus
    &:focus {
      border: 1px solid #FF0E53;
    }

    // Disabled state
    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
      pointer-events: none;

      .upload-icon {
        color: rgba(255, 255, 255, 0.3);
      }

      .upload-text {
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .uploaded-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid #141414;
    background: #141414;
    cursor: pointer;

    .preview-img-container {
      width: 200px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0;
      background:
        linear-gradient(45deg, #EBEBEB 25%, transparent 25%),
        linear-gradient(-45deg, #EBEBEB 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #EBEBEB 75%),
        linear-gradient(-45deg, transparent 75%, #EBEBEB 75%);
      background-size: 16px 16px;
      background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
      background-color: #FFFFFF;
    }

    .preview-img {
      max-width: 100%;
      max-height: 100%;
      width: 100%;
      height: 200px;
      object-fit: contain;
      display: block;
    }

    .image-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 20;

      .reupload-btn {
        min-width: 32px;
        height: 32px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 8px;
        border: none;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(20px);
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        cursor: pointer;

        &:hover {
          background: rgba(0, 0, 0, 0.6);
          color: white;
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  // Scanning overlay for image matting loading effect
  .scanning-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 15;
    overflow: hidden;
    border-radius: inherit;

    .scanning-line {
      position: absolute;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, transparent, #FF0E53, transparent);
      box-shadow: 0 0 10px #FF0E53;
      animation: scanning 2s linear infinite;
    }
  }

  .upload-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    z-index: 10;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 2px solid #333333;
      border-top: 2px solid #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 8px;
    }

    span {
      text-align: center;
      padding: 0 6px;
      font-size: 12px;
      line-height: 14px;
      color: rgba(255, 255, 255, 0.5);
    }
  }

  // 多张图片上传样式
  .multiple-images-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: flex-start;

    .multiple-image-item {
      position: relative;
      width: 110px;
      height: 110px;
      border-radius: 16px;
      overflow: hidden;
      cursor: pointer;

      .image-wrapper {
        width: 100%;
        height: 100%;
        position: relative;
        border: none;
        border-radius: 16px;
        overflow: hidden;
        background: #141414;

        .preview-img-container {
          width: 100%;
          height: 100%;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0;
          background:
            linear-gradient(45deg, #EBEBEB 25%, transparent 25%),
            linear-gradient(-45deg, #EBEBEB 25%, transparent 25%),
            linear-gradient(45deg, transparent 75%, #EBEBEB 75%),
            linear-gradient(-45deg, transparent 75%, #EBEBEB 75%);
          background-size: 8px 8px;
          background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
          background-color: #FFFFFF;
        }

        .preview-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }
      }

      .image-delete-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 32px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(20px);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        cursor: pointer;
        transition: background 0.2s ease;

        .delete-icon {
          font-size: 16px;
        }

        &:hover {
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }

    .upload-area-multiple {
      width: 110px;
      height: 110px;
      border: none;
      border-radius: 16px;
      background: #141414;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: none;
        color: rgba(255, 255, 255, 0.7);
        background: rgba(20, 20, 20, 0.7);
      }

      &:active {
        border-color: none;
        transform: scale(0.98);
      }

      .upload-icon-multiple {
        font-size: 36px;
        font-weight: normal;
        color: rgba(255, 255, 255, 0.9);
      }

      .upload-loading-multiple {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
        z-index: 10;

        .loading-spinner {
          width: 24px;
          height: 24px;
          border: 2px solid #333333;
          border-top: 2px solid #ffffff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 6px;
        }

        span {
          text-align: center;
          padding: 0 4px;
          font-size: 10px;
          line-height: 12px;
          color: rgba(255, 255, 255, 0.5);
        }
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;
        pointer-events: none;

        .upload-icon-multiple {
          color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes scanning {
  0% {
    top: -3px;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}
