import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect, useRef } from 'react';
import { Button, Toast } from 'dingtalk-design-mobile';
import $uploadImage from '@ali/dingtalk-jsapi/api/biz/util/uploadImage';
import $saveImage from '@ali/dingtalk-jsapi/api/biz/util/saveImage';
import uploadWebImage from '@ali/dd-upload';
import { AddToSFilled, DeleteOutlined } from '@ali/ding-icons';
import { previewImages } from '@/components/imagesPreview';
import { isDingTalk, isMobileDevice } from '@/utils/jsapi';
import { log } from '@/utils/console';
import { sendUT } from '@/utils/trace';
import request from '@/apis/base';
import { validateImageSize, ImageSizeConstraints } from '@/utils/imageValidation';
import './index.less';

interface ImageUploaderProps {
  value?: string | string[] | null;
  onChange?: (imageUrl: string | string[] | null) => void;
  sizeConstraints?: ImageSizeConstraints; // 图片尺寸限制配置
  scanningLoading?: boolean; // 抠图扫描加载状态
  hasCutBackground?: boolean; // 是否需要抠图背景
  uploadText?: string; // 上传文本
  showReuploadBtn?: boolean; // 是否显示重新上传按钮
  disabled?: boolean; // 是否禁用上传功能
  multiple?: boolean; // 是否支持多张图片上传
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  value,
  onChange,
  sizeConstraints, // 不设置默认值，如果没有传递则不校验
  scanningLoading = false, // 抠图扫描加载状态
  hasCutBackground = false, // 是否需要抠图背景
  uploadText = '', // 上传文本
  showReuploadBtn = true, // 是否显示重新上传按钮
  disabled = false, // 是否禁用上传功能
  multiple = false, // 是否支持多张图片上传
}) => {
  const [loading, setLoading] = useState(false);
  const uploadAreaRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null); // For web file input

  const isMobile = isMobileDevice();

  // 获取当前图片数组（兼容单张和多张模式）
  const currentImages = multiple
    ? (Array.isArray(value) ? value : (value ? [value] : []))
    : [];

  // 获取当前单张图片（单张模式）
  const currentSingleImage = multiple ? null : (typeof value === 'string' ? value : null);

  // 判断是否可以继续上传
  const canUploadMore = multiple ? currentImages.length < 3 : !currentSingleImage;

  // Auto focus on upload area when component mounts and no image is uploaded
  useEffect(() => {
    const hasNoImages = multiple ? currentImages.length === 0 : !currentSingleImage;
    if (hasNoImages && uploadAreaRef.current) {
      // Set focus on the upload area for accessibility
      uploadAreaRef.current.focus();
    }
  }, [multiple, currentImages.length, currentSingleImage]);

  // 保存图片到本地的函数 - Save image to local storage using DingTalk native API
  const saveImageToLocal = (imageUrl: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      $saveImage({
        image: imageUrl,
      })
        .then(() => {
          resolve();
        })
        .catch((error: Error) => {
          // eslint-disable-next-line no-console
          log.error('Failed to save image to local:', error);
          reject(error);
        });
    });
  };

  // 删除图片的函数
  const handleRemoveImage = (index: number) => {
    if (multiple) {
      const newImages = currentImages.filter((_, i) => i !== index);
      onChange?.(newImages.length > 0 ? newImages : null);
    } else {
      onChange?.(null);
    }
  };


  const handleImageClick = (imageUrl: string, index?: number) => {
    if (multiple && currentImages.length > 0) {
      // 多张图片模式，预览所有图片
      const photos = currentImages.map(url => ({
        src: url,
      }));
      previewImages({
        photos,
        current: index || 0,
      });
    } else if (!multiple && imageUrl) {
      // 单张图片模式
      const previewUrl = imageUrl;

      previewImages({
        photos: [{ src: previewUrl }],
        current: 0,
      });
    }
  };

  const handleReupload = (e: React.MouseEvent) => {
    sendUT('aigc_picture_reupdate', {
      device: isMobile ? 'mobile' : 'pc',
      imageUrl: value,
    });

    e.stopPropagation();
    handleUploadClick();
  };

  // Handle file selection for web environment
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      Toast.fail({
        content: i18next.t(
          'j-dingtalk-web_pages_aiVideo_components_ImageUploader_OnlyImageFilesAllowed',
        ),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    // Check file size (limit to 30MB)
    const maxSize = 30 * 1024 * 1024; // 30MB
    if (file.size > maxSize) {
      Toast.fail({
        content: i18next.t(
          'j-dingtalk-web_pages_aiVideo_components_ImageUploader_FileTooLarge',
        ),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    setLoading(true);
    try {
      // Upload file using @ali/dd-upload
      const uploadResult = await uploadWebImage({
        file,
        lwp: true,
        lwpRequest: (url, headers, body) => {
          return request(
            url,
            body,
            headers,
          );
        },
        id: 'dingRichTextEditor',
        downloadId: 'dingRichTextEditor',
      });

      if (uploadResult && uploadResult.originUrl) {
        // Validate and process image dimensions
        const validationResult = await validateImageSize(uploadResult.originUrl, sizeConstraints);
        if (validationResult.isValid) {
          // Use processed URL (may include scaling parameters)
          if (multiple) {
            const newImages = [...currentImages, validationResult.processedUrl];
            onChange?.(newImages);
          } else {
            onChange?.(validationResult.processedUrl);
          }

          Toast.success({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully',
            ),
            position: 'top',
            maskClickable: true,
            duration: 2,
          });
        }
      } else {
        Toast.fail({
          content: i18next.t(
            'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed',
          ),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
      }
    } catch (error) {
      log.error('Web upload failed:', error);
      Toast.fail({
        content: i18next.t(
          'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed',
        ),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    } finally {
      setLoading(false);
      // Clear the input value so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleUploadClick = async () => {
    if (!isDingTalk()) {
      // Trigger file input for web environment
      fileInputRef.current?.click();
      return;
    }

    setLoading(true);
    $uploadImage({
      multiple: false, // Single image upload
      compression: true, // Enable image compression
      max: 1, // Maximum 1 image
    })
      .then(async (result: any) => {
        log.info('upload result', result);
        // Handle upload success
        if (result && result.length > 0) {
          // Get the first uploaded image result
          const uploadResult = result[0];
          // Check if result is a string (image URL) or object with localId (camera mode)
          let imageUrl: string;

          if (typeof uploadResult === 'string') {
            // Direct image URL from gallery
            imageUrl = uploadResult;
          } else if (uploadResult && typeof uploadResult === 'object') {
            // Object result, might contain localId for camera mode
            imageUrl = uploadResult.url || uploadResult.mediaId || uploadResult;
          } else {
            imageUrl = uploadResult;
          }

          // 验证和处理图片尺寸 - Validate and process image dimensions
          const validationResult = await validateImageSize(imageUrl, sizeConstraints);
          if (validationResult.isValid) {
            // 使用处理后的图片URL（可能包含缩放参数）- Use processed URL (may include scaling parameters)
            if (multiple) {
              const newImages = [...currentImages, validationResult.processedUrl];
              onChange?.(newImages);
            } else {
              onChange?.(validationResult.processedUrl);
            }

            // 如果是移动端，保存图片到本地
            if (isDingTalk() && isMobileDevice()) {
              try {
                await saveImageToLocal(validationResult.processedUrl);
              } catch (error) {
                // eslint-disable-next-line no-console
                log.warn('Failed to save camera image to local:', error);
              }
            }

            Toast.success({
              content: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully',
              ),
              position: 'top',
              maskClickable: true,
              duration: 2,
            });
          }
        } else {
          Toast.fail({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed',
            ),
            position: 'top',
            maskClickable: true,
            duration: 3,
          });
        }
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <div className="image-uploader">
      {multiple ? (
        // 多张图片上传模式
        <div className="multiple-images-container">
          {currentImages.map((imageUrl, index) => (
            <div key={`${imageUrl}-${index}`} className="multiple-image-item">
              <div className="image-wrapper" onClick={() => handleImageClick(imageUrl, index)}>
                {hasCutBackground ? (
                  <div className="preview-img-container">
                    <img src={imageUrl} alt={`Uploaded ${index + 1}`} className="preview-img" />
                  </div>
                ) : (
                  <img src={imageUrl} alt={`Uploaded ${index + 1}`} className="preview-img" />
                )}
              </div>
              <div className="image-delete-overlay" onClick={(e) => {
                e.stopPropagation();
                handleRemoveImage(index);
              }}>
                <DeleteOutlined className="delete-icon" />
                {i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Delete')}
              </div>
            </div>
          ))}

          {/* 上传按钮，最多3张时不显示 */}
          {canUploadMore && (
            <div
              ref={uploadAreaRef}
              className={`upload-area-multiple ${disabled ? 'disabled' : ''}`}
              onClick={disabled ? undefined : handleUploadClick}
              tabIndex={disabled ? -1 : 0}
              role="button"
              aria-label={i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage',
              )}
              aria-disabled={disabled}
            >
              {!loading && (
                <AddToSFilled className="upload-icon-multiple" />
              )}
              {scanningLoading && (
                <div className="scanning-overlay">
                  <div className="scanning-line" />
                </div>
              )}
              {/* 多图模式的 loading */}
              {loading && (
                <div className="upload-loading-multiple">
                  <div className="loading-spinner" />
                  <span>
                    {i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading')}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      ) : (
        // 单张图片上传模式（保持原有逻辑）
        currentSingleImage ? (
          <div className="uploaded-image" onClick={() => handleImageClick(currentSingleImage)}>
            {hasCutBackground ? (
              <div className="preview-img-container">
                <img key={currentSingleImage} src={currentSingleImage} alt="Uploaded" className="preview-img" />
              </div>
            ) : (
              <img key={currentSingleImage} src={currentSingleImage} alt="Uploaded" className="preview-img" />
            )}
            {
              showReuploadBtn && (
                <div className="image-actions">
                  <Button size="small" onClick={handleReupload} className="action-btn reupload-btn">
                    {i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ReUpload')}
                  </Button>
                </div>
              )
            }
          </div>
        ) : (
          <div
            ref={uploadAreaRef}
            className={`upload-area ${disabled ? 'disabled' : ''}`}
            onClick={disabled ? undefined : handleUploadClick}
            tabIndex={disabled ? -1 : 0}
            role="button"
            aria-label={i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage',
            )}
            aria-disabled={disabled}
          >
            {!loading && (
              <>
                <AddToSFilled className="upload-icon" />
                <div className="upload-text">
                  {uploadText || i18next.t(
                    'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage',
                  )}
                </div>
              </>
            )}
            {scanningLoading && (
              <div className="scanning-overlay">
                <div className="scanning-line" />
              </div>
            )}
          </div>
        )
      )}

      {/* 单图模式的 loading */}
      {loading && !multiple && (
        <div className="upload-loading">
          <div className="loading-spinner" />
          <span>
            {i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading')}
          </span>
        </div>
      )}

      {/* Hidden file input for web environment */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default ImageUploader;
