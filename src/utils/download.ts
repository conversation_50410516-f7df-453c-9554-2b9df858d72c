import { i18next } from '@ali/dingtalk-i18n';
import { Toast } from 'dingtalk-design-mobile';
import $saveImage from '@ali/dingtalk-jsapi/api/biz/util/saveImage';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { downloadFileByUrl } from '@/apis/request';
import { isDingTalk, getJumpBrowserUrl, isPc } from './jsapi';
import { log } from './console';

// DingTalk saveImage API interface
interface DingTalkSaveImageOptions {
  image: string; // DingTalk requires 'image' property instead of 'url'
  success?: () => void;
  fail?: (err: Error) => void;
}

export interface DownloadOptions {
  filename?: string;
  showProgress?: boolean;
  timeout?: number; // Download timeout in milliseconds
}

// Browser detection utilities
interface BrowserInfo {
  isIOS: boolean;
  isSafari: boolean;
  supportsDownload: boolean;
  isMobile: boolean;
}

/**
 * Detect browser capabilities for download functionality
 */
const detectBrowser = (): BrowserInfo => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isIOS = /iphone|ipad|ipod/.test(userAgent);
  const isSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent);
  const isMobile = /mobile|android|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(userAgent);

  // Test if browser supports download attribute
  const testLink = document.createElement('a');
  const supportsDownload = typeof testLink.download !== 'undefined';

  return {
    isIOS,
    isSafari,
    supportsDownload,
    isMobile,
  };
};

/**
 * Get MIME type based on file extension
 */
const getMimeType = (filename: string): string => {
  const extension = filename?.toLowerCase()?.split('.')?.pop();
  const mimeTypes: Record<string, string> = {
    mp4: 'video/mp4',
    mov: 'video/quicktime',
    avi: 'video/x-msvideo',
    gif: 'image/gif',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    pdf: 'application/pdf',
    txt: 'text/plain',
    zip: 'application/zip',
  };

  return mimeTypes[extension || ''] || 'application/octet-stream';
};

/**
 * Enhanced download utility with cross-platform compatibility
 * @param url - The URL of the file to download
 * @param options - Download options
 */
export const downloadFile = async (
  url: string,
  options: DownloadOptions = {},
): Promise<void> => {
  const {
    filename = 'download',
    showProgress = true,
    timeout = 30000,
  } = options;

  // Determine file type for user feedback
  const isVideo = filename.includes('.mp4') || filename.includes('.mov');
  const isGif = filename.includes('.gif');

  let fileType = i18next.t('j-dingtalk-web_utils_download_File');
  if (isVideo) {
    fileType = i18next.t('j-dingtalk-web_utils_download_Video');
  } else if (isGif) {
    fileType = 'GIF';
  }

  if (isDingTalk() && !isPc) {
    // Use DingTalk's native download API only on mobile
    await downloadInDingTalk(url, filename, fileType, showProgress);
  } else {
    // Use enhanced browser download for PC (including DingTalk PC) and non-DingTalk environments
    await downloadInBrowser(url, filename, fileType, showProgress, timeout);
  }
};

/**
 * Download file using DingTalk's native API
 */
const downloadInDingTalk = async (
  url: string,
  filename: string,
  fileType: string,
  showProgress: boolean,
): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (showProgress) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_utils_download_DownloadingFiletype', { fileType }),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }

    // Determine file type and use appropriate DingTalk API
    const isImage = filename.includes('.gif') || filename.includes('.jpg') ||
    filename.includes('.jpeg') || filename.includes('.png');
    const isVideo = filename.includes('.mp4') || filename.includes('.mov') ||
    filename.includes('.avi');

    if (isImage) {
      // Use DingTalk's saveImage API for images
      downloadImageInDingTalk(url, fileType, resolve, reject);
    } else if (isVideo) {
      // For videos, use openLink to open in browser for download
      downloadVideoInDingTalk(url, fileType, resolve, reject);
    } else {
      // For other files, use openLink as fallback
      downloadOtherFileInDingTalk(url, fileType, resolve, reject);
    }
  });
};

/**
 * Download image using DingTalk's saveImage API
 */
const downloadImageInDingTalk = (
  url: string,
  fileType: string,
  resolve: () => void,
  reject: (err: Error) => void,
): void => {
  if ($saveImage) {
    const options: DingTalkSaveImageOptions = {
      image: url, // DingTalk uses 'image' property
      success: () => {
        Toast.success({
          content: i18next.t('j-dingtalk-web_utils_download_FiletypeDownloadedSuccessfully', { fileType }),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
        resolve();
      },
      fail: (err: Error) => {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_utils_download_FiletypeFailedToDownloadPlease', { fileType }),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
        reject(err);
      },
    };
    $saveImage(options);
  } else {
    Toast.fail({
      content: i18next.t('j-dingtalk-web_utils_download_DownloadIsNotSupportedIn'),
      duration: 3,
      position: 'top',
      maskClickable: true,
    });
    reject(new Error('DingTalk saveImage API not available'));
  }
};

/**
 * Download video using openLink to open in browser
 */
const downloadVideoInDingTalk = (
  url: string,
  fileType: string,
  resolve: () => void,
  reject: (err: Error) => void,
): void => {
  try {
    if ($openLink) {
      // DingTalk openLink doesn't support success/fail callbacks
      $openLink({ url: getJumpBrowserUrl(url) });
      Toast.success({
        content: i18next.t('j-dingtalk-web_utils_download_FiletypeIsOpenInThe', { fileType }),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
      resolve();
    } else {
      // Fallback: open in new window
      window.open(url, '_blank');
      Toast.success({
        content: i18next.t('j-dingtalk-web_utils_download_FiletypeIsOpenInA', { fileType }),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
      resolve();
    }
  } catch (error) {
    Toast.fail({
      content: i18next.t('j-dingtalk-web_utils_download_FiletypeFailedToDownloadPlease', { fileType }),
      duration: 3,
      position: 'top',
      maskClickable: true,
    });
    reject(error instanceof Error ? error : new Error('Unknown error'));
  }
};

/**
 * Download other files using openLink
 */
const downloadOtherFileInDingTalk = (
  url: string,
  fileType: string,
  resolve: () => void,
  reject: (err: Error) => void,
): void => {
  try {
    if ($openLink) {
      // DingTalk openLink doesn't support success/fail callbacks
      $openLink({ url: getJumpBrowserUrl(url) });
      Toast.success({
        content: i18next.t('j-dingtalk-web_utils_download_FiletypeIsOpenInThe', { fileType }),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
      resolve();
    } else {
      // Fallback: open in new window
      window.open(url, '_blank');
      Toast.success({
        content: i18next.t('j-dingtalk-web_utils_download_FiletypeIsOpenInA', { fileType }),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
      resolve();
    }
  } catch (error) {
    Toast.fail({
      content: i18next.t('j-dingtalk-web_utils_download_FiletypeFailedToDownloadPlease', { fileType }),
      duration: 3,
      position: 'top',
      maskClickable: true,
    });
    reject(error instanceof Error ? error : new Error('Unknown error'));
  }
};

/**
 * Download file using enhanced browser download with cross-platform compatibility
 */
const downloadInBrowser = async (
  url: string,
  filename: string,
  fileType: string,
  showProgress: boolean,
  timeout = 30000,
): Promise<void> => {
  const browserInfo = detectBrowser();

  try {
    if (showProgress) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_utils_download_DownloadingFiletype', { fileType }),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }

    // Try enhanced download method first
    const success = await downloadWithFetch(url, filename, browserInfo, timeout);

    if (!success) {
      // Fallback to simple download method
      await downloadWithLink(url, filename, browserInfo);
    }

    Toast.success({
      content: i18next.t('j-dingtalk-web_utils_download_StartDownloadingFiletype', { fileType }),
      duration: 2,
      position: 'top',
      maskClickable: true,
    });
  } catch (error) {
    Toast.fail({
      content: i18next.t('j-dingtalk-web_utils_download_FailedToDownloadFiletypePlease', { fileType }),
      duration: 2,
      position: 'top',
      maskClickable: true,
    });
    throw error;
  }
};

/**
 * Enhanced download using fetch and Blob for better compatibility
 */
const downloadWithFetch = async (
  url: string,
  filename: string,
  browserInfo: BrowserInfo,
  timeout: number,
): Promise<boolean> => {
  try {
    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    // Fetch file data
    const response = await fetch(url, {
      signal: controller.signal,
      mode: 'cors',
      credentials: 'omit',
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Get file data as blob
    const blob = await response.blob();
    const mimeType = getMimeType(filename);

    // Create blob with correct MIME type
    const file = new Blob([blob], { type: mimeType });

    // Create download URL
    const downloadUrl = URL.createObjectURL(file);

    try {
      if (browserInfo.isIOS && browserInfo.isSafari) {
        // Special handling for iOS Safari
        await downloadForIOSSafari(downloadUrl, filename);
      } else {
        // Standard download for other browsers
        await downloadWithBlobUrl(downloadUrl, filename, browserInfo);
      }

      return true;
    } finally {
      // Clean up the object URL
      setTimeout(() => URL.revokeObjectURL(downloadUrl), 1000);
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Download timeout');
    }
    // Fetch download failed, will try fallback method
    return false;
  }
};

/**
 * Download using blob URL for standard browsers
 */
const downloadWithBlobUrl = async (
  blobUrl: string,
  filename: string,
  browserInfo: BrowserInfo,
): Promise<void> => {
  const link = document.createElement('a');
  link.href = blobUrl;

  if (browserInfo.supportsDownload) {
    link.download = filename;
  }

  link.style.display = 'none';
  document.body.appendChild(link);

  try {
    link.click();
  } finally {
    document.body.removeChild(link);
  }
};

/**
 * Special download handling for iOS Safari
 */
const downloadForIOSSafari = async (blobUrl: string, filename: string): Promise<void> => {
  // iOS Safari doesn't support download attribute well
  // Open in new window and let user save manually
  const newWindow = window.open(blobUrl, '_blank');

  if (!newWindow) {
    // If popup blocked, try direct navigation
    window.location.href = blobUrl;
  }

  // Show instruction to user
  Toast.info({
    content: `Please use "Save As" to save the file: ${filename}`,
    duration: 3,
    position: 'top',
    maskClickable: true,
  });
};

/**
 * Fallback download method using simple link
 */
const downloadWithLink = async (
  url: string,
  filename: string,
  browserInfo: BrowserInfo,
): Promise<void> => {
  const link = document.createElement('a');
  link.href = url;

  if (browserInfo.supportsDownload && !browserInfo.isIOS) {
    link.download = filename;
  }

  link.style.display = 'none';
  document.body.appendChild(link);

  try {
    if (browserInfo.isIOS || !browserInfo.supportsDownload) {
      // Open in new window for iOS or browsers without download support
      link.target = '_blank';
    }

    link.click();
  } finally {
    document.body.removeChild(link);
  }
};

/**
 * Download video file with predefined settings
 */
export const downloadVideo = async (url: string, uuid: string): Promise<void> => {
  const filename = `ai_video_${uuid}.mp4`;

  try {
    // 优先使用 downloadFileByUrl 接口
    await downloadFileByUrl({ url, filename });
  } catch (error) {
    // 如果 downloadFileByUrl 接口下载失败，则使用 downloadFile 作为备选方案
    log.warn('downloadFileByUrl failed, falling back to downloadFile:', error);
    return downloadFile(url, {
      filename,
      showProgress: true,
    });
  }
};

/**
 * Download GIF file with predefined settings
 */
export const downloadGif = (url: string, uuid: string): Promise<void> => {
  return downloadFile(url, {
    filename: `ai_gif_${uuid}.gif`,
    showProgress: true,
  });
};

/**
 * Download image file with predefined settings
 */
export const downloadImage = (url: string, uuid: string): Promise<void> => {
  return downloadFile(url, {
    filename: `ai_image_${uuid}.jpg`,
    showProgress: true,
  });
};

/**
 * Download multiple video files in batch
 */
export const downloadVideosBatch = async (
  videos: Array<{ url: string; uuid: string }>,
  onProgress?: (completed: number, total: number) => void,
): Promise<{ success: number; failed: number }> => {
  if (!videos || videos.length === 0) {
    return { success: 0, failed: 0 };
  }

  const total = videos.length;
  let completed = 0;
  let successCount = 0;
  let failedCount = 0;

  Toast.info({
    content: '正在批量下载视频...',
    duration: 2,
    position: 'top',
    maskClickable: true,
  });

  const downloadPromises = videos.map(async (video) => {
    try {
      await downloadVideo(video.url, video.uuid);
      successCount++;
      completed++;
      if (onProgress) {
        onProgress(completed, total);
      }

      // Show progress update for every few videos
      if (completed % 3 === 0 || completed === total) {
        Toast.info({
          content: `下载进度: ${completed}/${total}`,
          duration: 1,
          position: 'top',
          maskClickable: true,
        });
      }
    } catch (error) {
      console.error(`Failed to download video ${video.uuid}:`, error);
      failedCount++;
      completed++; // Still count as completed to avoid blocking progress
      if (onProgress) {
        onProgress(completed, total);
      }
    }
  });

  // Wait for all downloads to complete
  await Promise.allSettled(downloadPromises);

  // Show final result
  if (failedCount === 0) {
    Toast.success({
      content: `批量下载完成: ${successCount}个`,
      duration: 3,
      position: 'top',
      maskClickable: true,
    });
  } else if (successCount === 0) {
    Toast.fail({
      content: '批量下载失败',
      duration: 3,
      position: 'top',
      maskClickable: true,
    });
  } else {
    Toast.info({
      content: `批量下载完成: ${successCount}个, 失败: ${failedCount}个`,
      duration: 4,
      position: 'top',
      maskClickable: true,
    });
  }

  return { success: successCount, failed: failedCount };
};
