export const COMMON_PARAMS = 'dd_full_screen=true&dd_darkmode=true&navTranslucent=true&dd_nav_translucent=true';

// Environment patterns
const ENV_PATTERNS = {
  LOCAL: /^(localhost:\d+|127\.0\.0\.1)$/,
  PRE_DINGTALK: /^pre-n\.dingtalk\.com$/,
  PROD_DINGTALK: /^n\.dingtalk\.com$/,
  PRE_7DING: /^pre-j\.7ding\.ai$/,
  PROD_7DING: /^7ding\.ai$/,
  PRE_JP_DINGTALK: /^pre-jp\.dingtalk\.com$/,
  PROD_JP_DINGTALK: /^jp\.dingtalk\.com$/,
} as const;

/**
 * @desc 通用的环境检测函数
 * @param {RegExp} pattern 环境匹配模式
 * @returns {boolean} 是否匹配该环境
 */
function checkEnvironment(pattern: RegExp): boolean {
  return pattern.test(window.location.host);
}

/**
 * 判断是本地 localhost 或者 127.0.0.1 的调试环境
 * <AUTHOR>
 * @returns {boolean}
 */
export function isLocalEnv(): boolean {
  return checkEnvironment(ENV_PATTERNS.LOCAL);
}

/**
 * @desc 判断当前是否预发域名
 * <AUTHOR>
 * @return {boolean}
 */
export function isPreEnv(): boolean {
  return checkEnvironment(ENV_PATTERNS.PRE_DINGTALK);
}

/**
 * @desc 判断当前是否生产环境
 * <AUTHOR>
 * @returns {boolean}
 */
export function isProdEnv(): boolean {
  return checkEnvironment(ENV_PATTERNS.PROD_DINGTALK);
}

/**
 * @desc 判断当前是否7ding.ai预发环境
 * <AUTHOR>
 * @return {boolean}
 */
export function is7dingPreEnv(): boolean {
  return checkEnvironment(ENV_PATTERNS.PRE_7DING);
}

/**
 * @desc 判断当前是否7ding.ai生产环境
 * <AUTHOR>
 * @returns {boolean}
 */
export function is7dingProdEnv(): boolean {
  return checkEnvironment(ENV_PATTERNS.PROD_7DING);
}

/**
 * @desc 判断当前是否jp.dingtalk.com预发环境
 * <AUTHOR>
 * @returns {boolean}
 */
export function isJpDingtalkPreEnv(): boolean {
  return checkEnvironment(ENV_PATTERNS.PRE_JP_DINGTALK);
}

/**
 * @desc 判断当前是否jp.dingtalk.com生产环境
 * <AUTHOR>
 * @returns {boolean}
 */
export function isJpDingtalkProdEnv(): boolean {
  return checkEnvironment(ENV_PATTERNS.PROD_JP_DINGTALK);
}

// Domain configurations
const DOMAINS = {
  DINGTALK: {
    PRE: 'https://pre-n.dingtalk.com',
    PROD: 'https://n.dingtalk.com',
  },
  SEVEN_DING: {
    PRE: 'pre-j.7ding.ai',
    PROD: '7ding.ai',
  },
  FLUXION: {
    PRE: 'https://pre-fluxion.dingtalk.com',
    PROD: 'https://fluxion.dingtalk.com',
  },
} as const;

/**
 * @desc 获取7ding.ai对应的域名（根据环境区分）
 * <AUTHOR>
 * @return {string} 返回对应环境的域名
 */
export function get7dingDomain(): string {
  return isPreEnv() ? DOMAINS.SEVEN_DING.PRE : DOMAINS.SEVEN_DING.PROD;
}

/**
 * @desc 获取n.dingtalk.com对应的域名（根据环境区分）
 * <AUTHOR>
 * @return {string} 返回对应环境的域名
 */
export function getNdingDomain(): string {
  return isPreEnv() ? DOMAINS.DINGTALK.PRE : DOMAINS.DINGTALK.PROD;
}

/**
 * @desc 获取fluxion.dingtalk.com对应的域名（根据环境区分）
 * <AUTHOR>
 * @return {string} 返回对应环境的域名
 */
export function getFluxionDomain(): string {
  return isJpDingtalkPreEnv() || is7dingPreEnv() || isLocalEnv() ? DOMAINS.FLUXION.PRE : DOMAINS.FLUXION.PROD;
}

/**
 * @desc 获取lwp寻源链接
 * <AUTHOR>
 * @param {string} dspcCode dspcCode
 * @returns {string} 寻源链接
 */
export function getLwpSourceUrl(dspcCode: string): string {
  return `${getNdingDomain()}/dingding/J-dingtalk/productOriginV2/index.html?productId=${dspcCode}`;
}

/**
 * @desc 通用的页面URL生成函数
 * <AUTHOR>
 * @param {string} pagePath 页面路径（如 'ai-image', 'ai-video', 'order-list'）
 * @returns {string} 生成的URL
 */
function generatePageUrl(pagePath: string): string {
  if (isLocalEnv()) {
    return `http://localhost:${window.location.port}/${pagePath}/index.html?${COMMON_PARAMS}`;
  }

  if (is7dingPreEnv() || is7dingProdEnv() || isJpDingtalkPreEnv() || isJpDingtalkProdEnv()) {
    return `https://${window.location.host}/${pagePath}?${COMMON_PARAMS}`;
  }

  return `${getNdingDomain()}/dingding/J-dingtalk/${pagePath}/index.html?${COMMON_PARAMS}`;
}

/** @desc 获取AI图片生成链接
 * <AUTHOR>
 * @returns {string} AI图片生成链接
 */
export function getAIImageUrl(): string {
  return generatePageUrl('ai-image');
}

/** @desc 获取AI视频生成链接
 * <AUTHOR>
 * @returns {string} AI视频生成链接
 */
export function getAIVideoUrl(): string {
  return generatePageUrl('ai-video');
}

/**
 * @desc 获取订单列表链接
 * <AUTHOR>
 * @returns {string} 订单列表链接
 */
export function getOrderListUrl(): string {
  return generatePageUrl('order-list');
}

/**
 * @desc 获取创建订单链接
 * <AUTHOR>
 * @returns {string} 创建订单链接
 */
export function getCreateOrderUrl(): string {
  return generatePageUrl('create-order');
}

/**
 * @desc 获取寻源链接
 * <AUTHOR>
 * @param {string} dspcCode dspcCode
 * @returns {string} 寻源链接
 */
export function getSourceUrl(dspcCode: string): string {
  return `https://${get7dingDomain()}/sourcing/${dspcCode}`;
}

/**
 * @desc 获取分享链接
 * <AUTHOR>
 * @returns {string} 分享链接
 */
export function getShareUrl(dspcCode: string): string {
  if (is7dingPreEnv() || is7dingProdEnv() || isJpDingtalkPreEnv() || isJpDingtalkProdEnv()) {
    return window.location.href;
  }

  return getSourceUrl(dspcCode);
}
