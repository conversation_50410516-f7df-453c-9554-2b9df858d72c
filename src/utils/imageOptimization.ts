/**
 * 图片URL优化工具函数
 * Image URL optimization utility functions
 */

// 支持的域名白名单
const ALLOWED_DOMAINS = [
  'down-cdn.dingtalk.com',
  'static.dingtalk.com',
  'i01.lw.aliimg.com',
];

/**
 * 检查URL是否在域名白名单中
 * Check if URL is in the allowed domain whitelist
 *
 * @param url - 图片URL
 * @returns boolean - 是否在白名单中
 */
export const isDomainAllowed = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return ALLOWED_DOMAINS.some(domain => urlObj.hostname.includes(domain));
  } catch {
    // 如果URL解析失败，返回false
    return false;
  }
};

/**
 * 解析URL中的尺寸参数
 * Parse size parameters from URL
 *
 * @param url - 图片URL
 * @returns {width: number, height: number} | null - 解析出的尺寸参数
 */
export const parseSizeParams = (url: string): { width: number; height: number } | null => {
  // 匹配 _WIDTHxHEIGHT.png 格式的尺寸参数
  const sizeRegex = /_(\d+)x(\d+)\.png/;
  const match = url.match(sizeRegex);

  if (match) {
    const width = parseInt(match[1], 10);
    const height = parseInt(match[2], 10);
    return { width, height };
  }

  return null;
};

/**
 * 生成优化的尺寸参数
 * Generate optimized size parameters
 *
 * @param originalWidth - 原始宽度
 * @param originalHeight - 原始高度
 * @param maxShortSide - 最大短边尺寸（默认400）
 * @returns string - 优化的尺寸参数字符串，如 "_400x10000.png"
 */
export const generateOptimizedSizeParams = (
  originalWidth: number,
  originalHeight: number,
  maxShortSide: number = 400
): string => {
  // 计算宽高比
  const aspectRatio = originalWidth / originalHeight;

  // 确定短边和长边
  const isWidthShorter = originalWidth <= originalHeight;

  if (isWidthShorter) {
    // 宽度是短边，将宽度设为maxShortSide，高度按比例计算
    const newHeight = Math.round(maxShortSide / aspectRatio);
    return `_${maxShortSide}x${newHeight}.png`;
  } else {
    // 高度是短边，将高度设为maxShortSide，宽度按比例计算
    const newWidth = Math.round(maxShortSide * aspectRatio);
    return `_${newWidth}x${maxShortSide}.png`;
  }
};

/**
 * 优化视频封面图片URL
 * Optimize video poster image URL
 *
 * @param originalUrl - 原始图片URL
 * @param maxShortSide - 最大短边尺寸（默认400）
 * @param useSizeOptimization - 是否启用尺寸优化（默认true）
 * @returns string - 优化后的URL
 */
export const optimizeVideoPosterUrl = (
  originalUrl: string,
  maxShortSide: number = 400,
  useSizeOptimization: boolean = true
): string => {
  // 检查URL是否有效
  if (!originalUrl || typeof originalUrl !== 'string') {
    return originalUrl;
  }

  // 检查域名是否在白名单中
  if (!isDomainAllowed(originalUrl)) {
    console.warn('Domain not in whitelist, returning original URL:', originalUrl);
    return originalUrl;
  }

  // 如果不启用尺寸优化，直接返回原始URL
  if (!useSizeOptimization) {
    return originalUrl;
  }

  try {
    // 解析现有的尺寸参数
    const existingSize = parseSizeParams(originalUrl);

    if (existingSize) {
      // 如果已经存在尺寸参数，替换为优化的尺寸
      const optimizedSizeParams = generateOptimizedSizeParams(
        existingSize.width,
        existingSize.height,
        maxShortSide
      );

      // 替换URL中的尺寸参数
      return originalUrl.replace(/_(\d+)x(\d+)\.png/, optimizedSizeParams);
    } else {
      // 如果没有尺寸参数，需要添加
      // 首先尝试获取图片的实际尺寸（异步操作）
      // 这里先添加一个默认的尺寸参数，后续可以通过其他方式优化
      const defaultSizeParams = `_${maxShortSide}x10000.png`;

      // 处理URL，添加尺寸参数
      if (originalUrl.includes('?')) {
        const [baseUrl, queryParams] = originalUrl.split('?');
        return `${baseUrl}${defaultSizeParams}?${queryParams}`;
      } else {
        return `${originalUrl}${defaultSizeParams}`;
      }
    }
  } catch (error) {
    console.warn('Failed to optimize image URL:', error);
    return originalUrl;
  }
};

/**
 * 异步优化视频封面图片URL（支持获取实际图片尺寸）
 * Asynchronously optimize video poster image URL (supports getting actual image dimensions)
 *
 * @param originalUrl - 原始图片URL
 * @param maxShortSide - 最大短边尺寸（默认400）
 * @param useSizeOptimization - 是否启用尺寸优化（默认true）
 * @returns Promise<string> - 优化后的URL
 */
export const optimizeVideoPosterUrlAsync = async (
  originalUrl: string,
  maxShortSide: number = 400,
  useSizeOptimization: boolean = true
): Promise<string> => {
  // 检查URL是否有效
  if (!originalUrl || typeof originalUrl !== 'string') {
    return originalUrl;
  }

  // 检查域名是否在白名单中
  if (!isDomainAllowed(originalUrl)) {
    console.warn('Domain not in whitelist, returning original URL:', originalUrl);
    return originalUrl;
  }

  // 如果不启用尺寸优化，直接返回原始URL
  if (!useSizeOptimization) {
    return originalUrl;
  }

  try {
    // 尝试获取图片的实际尺寸
    const img = new Image();
    img.crossOrigin = 'anonymous';

    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = originalUrl;
    });

    const { width: actualWidth, height: actualHeight } = img;

    // 生成优化的尺寸参数
    const optimizedSizeParams = generateOptimizedSizeParams(
      actualWidth,
      actualHeight,
      maxShortSide
    );

    // 检查是否已经存在尺寸参数
    const existingSize = parseSizeParams(originalUrl);

    if (existingSize) {
      // 替换现有的尺寸参数
      return originalUrl.replace(/_(\d+)x(\d+)\.png/, optimizedSizeParams);
    } else {
      // 添加新的尺寸参数
      if (originalUrl.includes('?')) {
        const [baseUrl, queryParams] = originalUrl.split('?');
        return `${baseUrl}${optimizedSizeParams}?${queryParams}`;
      } else {
        return `${originalUrl}${optimizedSizeParams}`;
      }
    }
  } catch (error) {
    console.warn('Failed to optimize image URL asynchronously, falling back to sync method:', error);
    // 如果异步获取失败，回退到同步方法
    return optimizeVideoPosterUrl(originalUrl, maxShortSide, useSizeOptimization);
  }
};
