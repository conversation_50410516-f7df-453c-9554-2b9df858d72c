import { i18next } from '@ali/dingtalk-i18n';
import { Toast } from 'dingtalk-design-mobile';
import uploadWebImage from '@ali/dd-upload';
import request from '@/apis/base';

export interface ImageSizeConstraints {
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface ImageValidationResult {
  isValid: boolean;
  processedUrl: string;
  originalSize?: {
    width: number;
    height: number;
  };
  processedSize?: {
    width: number;
    height: number;
  };
}

/**
 * 验证和处理图片尺寸的工具函数
 * Validate and process image dimensions utility function
 * 
 * @param imageUrl - 图片URL
 * @param sizeConstraints - 尺寸限制配置
 * @returns Promise<ImageValidationResult> - 验证结果
 */
export const validateImageSize = async (
  imageUrl: string,
  sizeConstraints?: ImageSizeConstraints,
): Promise<ImageValidationResult> => {
  // 如果没有传递尺寸限制参数，则跳过验证
  // Skip validation if no size constraints provided
  if (!sizeConstraints) {
    return { isValid: true, processedUrl: imageUrl };
  }

  try {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = imageUrl;
    });

    const { width, height } = img;
    const {
      minWidth = 0,
      minHeight = 0,
      maxWidth = Infinity,
      maxHeight = Infinity,
    } = sizeConstraints;
    // 检查图片是否小于最小尺寸
    // Check if image is smaller than minimum size
    if (width < minWidth || height < minHeight) {
      // 图片过小，使用Canvas放大并上传到CDN
      // Image too small, use Canvas to enlarge and upload to CDN
      return await processImageWithCanvasEnlargement(imageUrl, minWidth, minHeight);
    }
    // 检查图片是否大于最大尺寸
    // Check if image is larger than maximum size
    if (width > maxWidth || height > maxHeight) {
      // 图片过大，使用URL参数进行缩小
      // Image too large, use URL parameters for shrinking
      const widthRatio = width / maxWidth;
      const heightRatio = height / maxHeight;

      let resizeParams = '';
      if (widthRatio >= heightRatio) {
        // 宽度超出更多，按宽度缩放
        // Width exceeds more, scale by width
        resizeParams = `_${maxWidth}x10000.png`;
      } else {
        // 高度超出更多，按高度缩放
        // Height exceeds more, scale by height
        resizeParams = `_10000x${maxHeight}.png`;
      }

      // 处理URL，添加缩放参数
      // Process URL, add scaling parameters
      let processedUrl = imageUrl;
      if (processedUrl.includes('?')) {
        const [baseUrl, queryParams] = processedUrl.split('?');
        processedUrl = `${baseUrl}${resizeParams}?${queryParams}`;
      } else {
        processedUrl = `${processedUrl}${resizeParams}`;
      }

      return {
        isValid: true,
        processedUrl,
        originalSize: { width, height },
        processedSize: {
          width: widthRatio >= heightRatio ? maxWidth : Math.round(width * (maxHeight / height)),
          height: widthRatio >= heightRatio ? Math.round(height * (maxWidth / width)) : maxHeight
        }
      };
    }
    // 图片尺寸符合要求
    // Image dimensions meet requirements
    return {
      isValid: true,
      processedUrl: imageUrl,
      originalSize: { width, height },
      processedSize: { width, height }
    };
  } catch (error) {
    Toast.fail({
      content: i18next.t(
        'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageLoadError',
      ),
      position: 'top',
      maskClickable: true,
      duration: 3,
    });
    return { isValid: false, processedUrl: imageUrl };
  }
};

/**
 * 获取图片尺寸信息的工具函数
 * Get image dimensions utility function
 *
 * @param imageUrl - 图片URL
 * @returns Promise<{width: number, height: number}> - 图片尺寸
 */
export const getImageDimensions = (imageUrl: string): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    }
    img.src = imageUrl;
  });
};

/**
 * 使用Canvas放大图片
 * Enlarge image using Canvas API
 *
 * @param imageUrl - 图片URL
 * @param targetWidth - 目标宽度
 * @param targetHeight - 目标高度
 * @param quality - 图片质量 (0-1)
 * @returns Promise<string> - 放大后的图片DataURL
 */
export const enlargeImageWithCanvas = (
  imageUrl: string,
  targetWidth: number,
  targetHeight: number,
  quality: number = 0.9
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous'; // 处理跨域图片

    img.onload = () => {
      try {
        // 创建Canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }

        // 设置Canvas尺寸
        canvas.width = targetWidth;
        canvas.height = targetHeight;

        // 使用高质量的图像渲染
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // 计算图片在Canvas中的位置（居中）
        const imgAspectRatio = img.width / img.height;
        const canvasAspectRatio = targetWidth / targetHeight;

        let drawWidth, drawHeight, drawX, drawY;

        if (imgAspectRatio > canvasAspectRatio) {
          // 图片比Canvas更宽，以Canvas宽度为准
          drawWidth = targetWidth;
          drawHeight = targetWidth / imgAspectRatio;
          drawX = 0;
          drawY = (targetHeight - drawHeight) / 2;
        } else {
          // 图片比Canvas更高，以Canvas高度为准
          drawHeight = targetHeight;
          drawWidth = targetHeight * imgAspectRatio;
          drawX = (targetWidth - drawWidth) / 2;
          drawY = 0;
        }

        // 绘制图片
        ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

        // 转换为DataURL
        const enlargedImageUrl = canvas.toDataURL('image/jpeg', quality);
        resolve(enlargedImageUrl);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for enlargement'));
    };

    img.src = imageUrl;
  });
};

/**
 * 将DataURL转换为File对象
 * Convert DataURL to File object
 *
 * @param dataUrl - DataURL字符串
 * @param filename - 文件名
 * @returns File对象
 */
export const dataURLToFile = (dataUrl: string, filename: string): File => {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  const blob = new Blob([u8arr], { type: mime });
  return new File([blob], filename, { type: mime });
};

/**
 * 计算图片放大参数
 * Calculate image scaling parameters for enlargement
 *
 * @param originalWidth - 原始宽度
 * @param originalHeight - 原始高度
 * @param targetMinWidth - 目标最小宽度
 * @param targetMinHeight - 目标最小高度
 * @returns 放大后的尺寸和缩放比例
 */
export const calculateEnlargementParams = (
  originalWidth: number,
  originalHeight: number,
  targetMinWidth: number,
  targetMinHeight: number
): {
  targetWidth: number;
  targetHeight: number;
  scaleRatio: number;
  needsEnlargement: boolean;
} => {
  // 检查是否已经满足最小尺寸要求
  if (originalWidth >= targetMinWidth && originalHeight >= targetMinHeight) {
    return {
      targetWidth: originalWidth,
      targetHeight: originalHeight,
      scaleRatio: 1,
      needsEnlargement: false
    };
  }

  // 计算放大比例
  const widthRatio = targetMinWidth / originalWidth;
  const heightRatio = targetMinHeight / originalHeight;

  // 取最大比例确保同时满足最小尺寸要求
  const scaleRatio = Math.max(widthRatio, heightRatio);

  // 计算放大后的尺寸
  const targetWidth = Math.round(originalWidth * scaleRatio);
  const targetHeight = Math.round(originalHeight * scaleRatio);

  return {
    targetWidth,
    targetHeight,
    scaleRatio,
    needsEnlargement: true
  };
};

/**
 * 处理图片放大并上传到CDN
 * Process image enlargement and upload to CDN
 *
 * @param imageUrl - 原始图片URL
 * @param targetMinWidth - 目标最小宽度
 * @param targetMinHeight - 目标最小高度
 * @param quality - 图片质量 (0-1)
 * @returns Promise<ImageValidationResult> - 处理结果
 */
export const processImageWithCanvasEnlargement = async (
  imageUrl: string,
  targetMinWidth: number,
  targetMinHeight: number,
  quality: number = 0.9
): Promise<ImageValidationResult> => {
  try {
    // 获取原始图片尺寸
    const originalSize = await getImageDimensions(imageUrl);

    // 计算放大参数
    const enlargementParams = calculateEnlargementParams(
      originalSize.width,
      originalSize.height,
      targetMinWidth,
      targetMinHeight
    );

    // 如果不需要放大，返回原始URL
    if (!enlargementParams.needsEnlargement) {
      return {
        isValid: true,
        processedUrl: imageUrl,
        originalSize,
        processedSize: originalSize
      };
    }

    // 使用Canvas放大图片
    const enlargedDataUrl = await enlargeImageWithCanvas(
      imageUrl,
      enlargementParams.targetWidth,
      enlargementParams.targetHeight,
      quality
    );

    // 将DataURL转换为File对象
    const enlargedFile = dataURLToFile(enlargedDataUrl, `enlarged_image_${Date.now()}.png`);
    // 上传放大后的图片到CDN
    const uploadResult = await uploadWebImage({
      file: enlargedFile,
      lwp: true,
      lwpRequest: (url, headers, body) => {
        return request(url, body, headers);
      },
      id: 'dingRichTextEditor',
      downloadId: 'dingRichTextEditor',
    });
    if (uploadResult && uploadResult.originUrl) {
      return {
        isValid: true,
        processedUrl: uploadResult.originUrl,
        originalSize,
        processedSize: {
          width: enlargementParams.targetWidth,
          height: enlargementParams.targetHeight
        }
      };
    } else {
      throw new Error('CDN upload failed');
    }
  } catch (error) {
    console.error('Canvas enlargement and CDN upload failed:', error);
    Toast.fail({
      content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageProcessError'),
      position: 'top',
      maskClickable: true,
      duration: 3,
    });

    return {
      isValid: false,
      processedUrl: imageUrl
    };
  }
};
