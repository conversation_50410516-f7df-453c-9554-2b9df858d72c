@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.work-center {
  background: @common_bg_color;
  padding-top: ~'max(var(--safe-area-inset-top), 10px)';
  padding-bottom: var(--safe-area-inset-bottom);
  display: flex;
  flex-direction: column;
  height: 100vh;

  & &__nav-bar {
    background-color: @common_bg_color;
    border-bottom: 1px solid transparent;
    flex: 0 0 auto;

    &::after {
      display: none;
    }
  }

  &__container {
    flex: 1;
    padding: 16px 16px 100px;
    margin: 0 auto;
    overflow-y: auto;
  }

  &__category {
    margin-bottom: 12px;
    border-radius: 16px;
    padding: 16px;
    background-color: @common_bg_z1_color;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__category-title {
    /* 正文/17B_Body */
    font-size: 17px;
    font-weight: 500;
    line-height: 24px;
    color: @common_level1_base_color;
    margin-bottom: 16px;
    padding-left: 4px;
  }

  &__app-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  // thumb-show 类型特殊样式
  &__category--thumb-show {
    .work-center__category-title {
      margin-bottom: 20px;
    }
  }

  &__thumb-show-layout {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &__top-app {
    display: flex;
    align-items: center;

    &-icon {
      width: 40px;
      height: 40px;
      margin-right: 8px;
      border-radius: 33%;
    }

    &-name {
      flex: 1;
      color: @common_level1_base_color;
      font-size: 14px;
    }

    &-next {
      color: @common_level3_base_color;
      font-size: 12px;
      margin-left: 8px;
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }
  }

  &__thumb-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    .work-center__thumb-app {
      &-thumb {
        width: 100%;
        border-radius: 8px;
        border: 0.5px solid @common_line_hard_color;
      }

      &-name {
        margin-top: 8px;
        font-size: 12px;
        color: @common_level1_base_color;
      }
    }
  }

  &__pc {
    .work-center__container {
      margin: 0;
    }
  }
}
