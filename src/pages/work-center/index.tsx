import { i18next } from '@ali/dingtalk-i18n';
import { isMobileDevice } from '@/utils/jsapi';
import React from 'react';
import { useTitle } from '@/hooks/useTitle';
import { IAppEntry, IAppCategory } from './types';
import AppCard from './components/AppCard';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import './index.less';
import ut$ from '@ali/dingtalk-jsapi/api/biz/util/ut';
import { RightArrowOutlined } from '@ali/ding-icons';
import { NavBar } from 'dingtalk-design-mobile';

const WorkCenter: React.FC = () => {
  const currentLangType = i18next.language === 'en_US' ? 'en' : (i18next.language === 'zh_CN' ? 'zh' : 'jp');

  useTitle(i18next.t('j-dingtalk-web_pages_work-center_Workbench'));

  ut$({
    key: 'aigc_workspace_click',
  });

  // 移动端应用数据
  const appData: IAppCategory[] = [
    {
      id: 'ecommerce',
      title: i18next.t('j-dingtalk-web_pages_work-center_ECommerceApplications'),
      description: i18next.t('j-dingtalk-web_pages_work-center_ECommerceRelatedApplicationTools'),
      apps: [
        {
          id: 'ai-video',
          name: i18next.t('j-dingtalk-web_pages_work-center_AiMaterial'),
          icon: 'https://img.alicdn.com/imgextra/i4/O1CN01GrUK6D28FdBw3jk0v_!!6000000007903-2-tps-120-120.png',
          url: 'dingtalk://dingtalkclient/action/open_platform_link?pcLink=dingtalk%3A%2F%2Fdingtalkclient%2Fpage%2Flink%3Furl%3Dhttps%253A%252F%252F7ding.ai%252Fai-studio%253Fdd_full_screen%253Dtrue%2526dd_darkmode%253Dtrue%2526navTranslucent%253Dtrue%2526dd_nav_translucent%253Dtrue%2526from%253D7ding%2526ddtab%253Dtrue&mobileLink=https%3A%2F%2F7ding.ai%2Fai-studio%3Fdd_full_screen%3Dtrue%26dd_darkmode%3Dtrue%26navTranslucent%3Dtrue%26dd_nav_translucent%3Dtrue%26from%3D7ding',
        },
        // {
        //   id: 'advertising',
        //   name: i18next.t('j-dingtalk-web_pages_work-center_SupplyChainManagement'),
        //   icon: 'https://img.alicdn.com/imgextra/i4/O1CN01Roko8b1POBsp76IJO_!!6000000001830-2-tps-120-120.png',
        //   url: 'https://alidocs.dingtalk.com/table/template/1053',
        // },
      ],
    },
    {
      id: 'general',
      title: i18next.t('j-dingtalk-web_pages_work-center_GeneralApplication'),
      description: i18next.t('j-dingtalk-web_pages_work-center_GeneralOfficeApplications'),
      apps: [
        {
          id: 'document',
          name: i18next.t('j-dingtalk-web_pages_work-center_Document'),
          icon: 'https://img.alicdn.com/imgextra/i3/O1CN018Bs3B21lWMUqErtA1_!!6000000004826-2-tps-120-120.png',
          url: 'https://alidocs.dingtalk.com/i?dd_full_screen=true&dd_mini_app_id=5000000004278063&dd_darkmode=true',
        },
        {
          id: 'schedule',
          name: i18next.t('j-dingtalk-web_pages_work-center_Schedule'),
          icon: 'https://img.alicdn.com/imgextra/i1/O1CN01SRnMgu1uI0cwRwPwg_!!6000000006013-2-tps-120-120.png',
          url: 'https://applink.dingtalk.com/action/switchtab?index=1&type=calendar&isToday=1',
        },
        {
          id: 'meeting',
          name: i18next.t('j-dingtalk-web_pages_work-center_Meeting'),
          icon: 'https://img.alicdn.com/imgextra/i1/O1CN01yAFxla1dIbfv1BcXz_!!6000000003713-2-tps-120-120.png',
          url: 'dingtalk://dingtalkclient/page/videoConfList',
        },
        {
          id: 'mail',
          name: i18next.t('j-dingtalk-web_pages_work-center_EmailAddress'),
          icon: 'https://img.alicdn.com/imgextra/i3/O1CN01zLuVZU1cA4gKDeS2C_!!6000000003559-2-tps-120-120.png',
          url: 'dingtalk://dingtalkclient/page/maillist',
        }],
    },
    {
      id: 'ai-table',
      title: i18next.t('j-dingtalk-web_pages_work-center_AiTable'),
      type: 'thumb-show',
      topApp: {
        id: 'ai-table',
        name: i18next.t('j-dingtalk-web_pages_work-center_AiTable'),
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01uwsoyF1Kk6l4jhioh_!!6000000001201-2-tps-120-120.png',
        url: 'https://alidocs.dingtalk.com/table?dd_full_screen=true&dd_mini_app_id=5000000006150316&dd_darkmode=true',
      },
      apps: [

        {
          id: 'commodity-sales-statistics',
          name: i18next.t('j-dingtalk-web_pages_work-center_CommoditySalesStatistics'),
          thumb: 'https://img.alicdn.com/imgextra/i4/O1CN01V4Ewvn1K9T4hMNrRI_!!6000000001121-2-tps-450-306.png',
          url: 'https://alidocs.dingtalk.com/table/template/1054?dd_full_screen=true&dd_mini_app_id=5000000006150316&dd_darkmode=true',
        },
        {
          id: 'inventory-management',
          name: i18next.t('j-dingtalk-web_pages_work-center_InventoryManagement'),
          thumb: 'https://img.alicdn.com/imgextra/i4/O1CN01lGIsyS26wZ5ZlczQS_!!6000000007726-2-tps-450-306.png',
          url: 'https://alidocs.dingtalk.com/table/template/1053?dd_full_screen=true&dd_mini_app_id=5000000006150316&dd_darkmode=true',
        },
        {
          id: 'user-evaluation-analysis-table',
          name: i18next.t('j-dingtalk-web_pages_work-center_UserEvaluationAnalysisTable'),
          thumb: 'https://img.alicdn.com/imgextra/i2/O1CN01yXtnrQ1DHWjXL4eMw_!!6000000000191-2-tps-450-306.png',
          url: 'https://alidocs.dingtalk.com/table/template/1113?dd_full_screen=true&dd_mini_app_id=5000000006150316&dd_darkmode=true',
        },
        {
          id: 'supply-chain-management',
          name: i18next.t('j-dingtalk-web_pages_work-center_SupplyChainManagement'),
          thumb: 'https://img.alicdn.com/imgextra/i4/O1CN01a1QWkU1QFJpSHgxTT_!!6000000001946-2-tps-450-306.png',
          url: 'https://alidocs.dingtalk.com/table/template/1181?dd_full_screen=true&dd_mini_app_id=5000000006150316&dd_darkmode=true',
        }],
    }];

  // PC 端应用数据
  const pcAppData: IAppCategory[] = [{
    id: 'ecommerce',
    title: i18next.t('j-dingtalk-web_pages_work-center_ECommerceApplications'),
    description: i18next.t('j-dingtalk-web_pages_work-center_ECommerceRelatedApplicationTools'),
    apps: [
      {
        id: 'ai-selection',
        name: i18next.t('j-dingtalk-web_pages_work-center_AiSelection'),
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01WraFR51gquBrL75dD_!!6000000004194-2-tps-311-311.png',
        url: `https://ding.proboost.microdata-inc.com/tiktok/Home?lang_type=${currentLangType}&from=7ding`,
        onClick: (app) => {
          window.open(app.url, '_blank');
        },
      },
    ],
  }];

  // 根据设备类型选择数据源
  const currentAppData = isMobileDevice() ? appData : pcAppData;

  // 获取当前URL中的corpId
  const getCurrentCorpId = (): string | null => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('corpid');
  };

  // 处理应用点击
  const handleAppClick = async (app: IAppEntry) => {
    if (app.url) {
      try {
        // 获取当前corpId
        const corpId = getCurrentCorpId();

        // 构建完整的URL，支持corpId替换
        let finalUrl = app.url;
        if (corpId) {
          const url = new URL(app.url, window.location.origin);
          url.searchParams.set('corpid', corpId);
          finalUrl = url.toString();
        }

        // 使用钉钉JSAPI打开链接
        await $openLink({ url: finalUrl });
      } catch (error) {
        console.error(i18next.t('j-dingtalk-web_pages_work-center_FailedToOpenTheLink'), error);
        // 降级处理：如果JSAPI失败，使用普通方式打开
        if (app.onClick) {
          app.onClick(app);
        }
      }
    } else if (app.onClick) {
      app.onClick(app);
    }
  };

  return (
    <div className={`work-center ${isMobileDevice() ? 'work-center__mobile' : 'work-center__pc'}`}>
      <NavBar className="work-center__nav-bar">{i18next.t('j-dingtalk-web_pages_work-center_Workbench')}</NavBar>
      <div className="work-center__container">
        {currentAppData.map((category) =>
          (<div key={category.id} className={`work-center__category ${category.type === 'thumb-show' ? 'work-center__category--thumb-show' : ''}`}>
            {category.type === 'thumb-show' ?
              <div className="work-center__thumb-show-layout">
                {/* 顶部主应用 */}
                {category.topApp &&
                  <div className="work-center__top-app" onClick={() => handleAppClick(category.topApp)}>
                    <img className="work-center__top-app-icon" src={category.topApp.icon} alt={category.topApp.name} />
                    <div className="work-center__top-app-name">{category.topApp.name}</div>
                    <div className="work-center__top-app-next">{i18next.t('j-dingtalk-web_pages_work-center_Detailed')}<RightArrowOutlined /></div>
                  </div>
                }

                {/* 2x2 网格布局 */}
                <div className="work-center__thumb-grid">
                  {category.apps.map((app) =>
                    (<div className="work-center__thumb-app" key={app.id} onClick={() => handleAppClick(app)}>
                      <img className="work-center__thumb-app-thumb" src={app.thumb} alt={app.name} />
                      <div className="work-center__thumb-app-name">{app.name}</div>
                    </div>))}
                </div>
              </div> :
              <>
                <h2 className="work-center__category-title">{category.title}</h2>
                <div className="work-center__app-grid">
                  {category.apps.map((app) =>
                    (<AppCard
                      key={app.id}
                      app={app}
                      onClick={() => handleAppClick(app)}
                    />))}
                </div>
              </>
            }
           </div>))}
      </div>
    </div>);
};

export default WorkCenter;
