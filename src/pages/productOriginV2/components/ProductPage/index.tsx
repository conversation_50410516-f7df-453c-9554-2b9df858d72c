import { i18next, isChinese } from '@ali/dingtalk-i18n';
import React, { useState, useEffect } from 'react';
import $setRight from '@ali/dingtalk-jsapi/api/biz/navigation/setRight';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import { IOriginProduct, IProductData, IUTParams, EPlatform } from '@/common/types';
import { Divider, Rate, Toast, Modal, Button } from 'dingtalk-design-mobile';
import { getDecodedUrlParam, getUrlParam, getPageConfig } from '@/utils/util';
import { isDingTalk, isPc, openLink, setPageTitle } from '@/utils/jsapi';
import { getShareUrl } from '@/utils/env';
import { sendUT } from '@/utils/trace';
import { queryImageSearch, queryProductByImage, reportProduct, reportFakeProduct, productAudit } from '@/apis';
import { createProductRankingsArray } from '@/utils/productRankings';
import OptimizedImage from '@/components/OptimizedImage';
import { WarningOutlined, ErrorFilled, CheckCircleFilled, CloseCircledFilled } from '@ali/ding-icons';
import { decode } from 'he';
import Skeleton from '../Skeleton';
import './style.less';

// 设置分享按钮
const setShare = (productData: IProductData, title: string, utParams: IUTParams) => {
  const productId = getUrlParam('productId') || productData?.productId || getPageConfig('productId');

  if (!isDingTalk() || isPc || !productId) {
    return;
  }

  $setRight({
    show: true,
    control: true,
    text: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_Share'),
    // @ts-ignore
    url: 'https://gw.alicdn.com/imgextra/i3/O1CN017yXnxD1gsjNgj1zev_!!6000000004198-2-tps-72-72.png',
    onSuccess: () => {
      // 页面曝光
      sendUT('sourcing_page_share_success', utParams);
      $setShare({
        type: 0,
        url: getShareUrl(productId), // Use 7ding domain for sharing
        title: decode(productData?.title) || title || '',
        content: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ShareContent'),
        image: productData?.image,
      });
    },
  });
};

const rankTypeMap = {
  salesProductList: [
    {
      type: 'salesCount',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume'),
    }],

  newProductList: [
    {
      type: 'favNum',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfLikes'),
    }],

  soaringProductList: [
    {
      type: 'salesCount',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume'),
    },
    {
      type: 'salesCountGrowthRate',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_DailyGrowth'),
    }],

  opportunityProductList: [
    {
      type: 'daysCount',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfDaysOnThe'),
    },
    {
      type: 'salesCount',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_SalesVolume'),
    }],

};

const originTagMap = [
  {
    type: 'monthSoldDisplay',
    name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_MonthlySales'),
    unit: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pieces'),
  },
  {
    type: 'repurchaseRate',
    name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_RepurchaseRate'),
    unit: '',
  }];

const platformPriceMap = {
  [EPlatform.ZOZOTOWN]: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ZozoGrid'),
  [EPlatform.AMAZON]: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_AmazonGrid'),
  [EPlatform.REDKOL]: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_RednoteGrid'),
  [EPlatform.TIKTOK]: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_TiktokGrid'),
  [EPlatform.TAOBAO]: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_TaobaoGrid'),
  [EPlatform.ALIBABA]: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_AlibabaGrid'),
  [EPlatform.RAKUTEN]: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_RakutenGrid'),
  [EPlatform.MANUAL]: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ManualGrid'),
};

const platform = getUrlParam('platform') as EPlatform;
// Use utility function to generate product rankings
const productRankings = createProductRankingsArray(platform);

const ProductPage: React.FC = () => {
  const [productData, setProductData] = useState<IProductData>();
  const [list, setList] = useState<IOriginProduct[]>([]);
  const title = getDecodedUrlParam('productTitle');
  const url = getDecodedUrlParam('url');
  const price = getUrlParam('price') || '';
  const [loading, setLoading] = useState<boolean>(true);
  const rankType = getUrlParam('listKey');
  const bizId = getDecodedUrlParam('bizId');

  // State for tracking consecutive clicks
  const [clickCount, setClickCount] = useState<number>(0);
  const [lastClickTime, setLastClickTime] = useState<number>(0);

  // State for tracking reported products
  const [reportedProducts, setReportedProducts] = useState<Set<string>>(new Set());

  // Handle consecutive clicks for copying productId
  const handleImageClick = () => {
    const currentTime = Date.now();
    const timeDiff = currentTime - lastClickTime;

    // Reset click count if more than 2 seconds have passed since last click
    if (timeDiff > 2000) {
      setClickCount(1);
    } else {
      setClickCount((prev) => prev + 1);
    }

    setLastClickTime(currentTime);

    // If clicked 6 times consecutively, copy productId to clipboard
    if (clickCount + 1 >= 6) {
      const productId = getUrlParam('productId') || getPageConfig('productId') || '';
      if (productId) {
        navigator.clipboard.writeText(productId);
        Toast.success({
          content: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_CopiedToClipboard'),
          position: 'top',
          maskClickable: true,
          duration: 2,
        });
      }
      // Reset click count after copying
      setClickCount(0);
    }
  };

  // 初始化商品元素引用数组
  useEffect(() => {
    if (isDingTalk()) {
      // 页面曝光
      sendUT('sourcing_page_view', {
        platform,
        reportType: getUrlParam('reportType'),
        bizId,
        rankType,
        productId: getUrlParam('productId'),
        productTitle: title,
      });
    }

    setPageTitle(i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing'));

    initData();
  }, []);

  // 初始化数据
  const initData = () => {
    setLoading(true);
    const productId = getUrlParam('productId') || getPageConfig('productId');
    if (isDingTalk()) {
      queryImageSearch({
        originProductId: productId,
        urls: [url],
        title,
        bizId,
      })
        .then((res) => {
          if (!res.success) {
            Toast.fail({
              content:
            res.message ||
            i18next.t(
              'j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData',
            ),
              duration: 3,
              position: 'top',
              maskClickable: true,
            });
            return;
          }

          setProductData(res.originProduct);
          setList(res.products);
          setLoading(false);
          setShare(res.originProduct, title, {
            platform,
            reportType: getUrlParam('reportType'),
            bizId,
            rankType,
            productId: getUrlParam('productId'),
            productTitle: title,
          });
        })
        .catch((err) => {
          Toast.fail({
            content:
          err?.errorMessage ||
          i18next.t(
            'j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData',
          ),
            duration: 3,
            position: 'top',
            maskClickable: true,
          });
        });
    } else {
      queryProductByImage({
        originProductId: productId,
      })
        .then((res) => {
          const { data } = res;
          if (!data?.success) {
            Toast.fail({
              content:
            data?.errorMessage ||
            i18next.t(
              'j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData',
            ),
              duration: 3,
              position: 'top',
              maskClickable: true,
            });
            return;
          }
          setProductData(data?.originProduct || {});
          setList(data?.products || []);
          setLoading(false);
          setShare(data.originProduct, title, {
            platform,
            reportType: getUrlParam('reportType'),
            bizId,
            rankType,
            productId: getUrlParam('productId'),
            productTitle: title,
          });
        })
        .catch((err) => {
          Toast.fail({
            content:
          err.message ||
          i18next.t(
            'j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData',
          ),
            duration: 3,
            position: 'top',
            maskClickable: true,
          });
        });
    }
  };

  // 打开商品详情
  const openProductDetail = (item: IOriginProduct) => {
    if (reportedProducts.has(item.productId)) {
      return;
    }

    if (isDingTalk()) {
      // 商品点击
      sendUT('sourcing_product_detail_click', {
        platform,
        reportType: getUrlParam('reportType'),
        bizId,
        rankType,
        productId: item.productId,
        productTitle: item.titleTranslated || item.title,
      });

      openLink(item.detailHtml, true);
    } else {
      window.open(item.detailHtml, '_blank');
    }
  };

  // Handle product report with optimized logic
  const handleReportClick = (e: React.MouseEvent, productId: string) => {
    // Early returns for invalid states
    if (reportedProducts.has(productId) || !productId) {
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    const productIdFromUrl = getUrlParam('productId');
    const productIdFromConfig = getPageConfig('productId');

    // Show confirmation dialog
    Modal.alert(i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFakeAndInferiorProducts'), i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportThatThisProductIs'),
      [
        {
          text: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotYet'),
          onClick: () => {


            // User cancelled report - no action needed
          } }, { text: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_ConfirmReport'),
          style: { color: '#FF0E53' },
          onClick: () => handleReportConfirm(productId, productIdFromUrl, productIdFromConfig),
        }]);
  };

  // Handle report confirmation with improved error handling
  const handleReportConfirm = async (
    productId: string,
    productIdFromUrl: string | null,
    productIdFromConfig: string | null,
  ) => {
    // Validate required parameters
    if (!productIdFromUrl && !productIdFromConfig) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_TheParameterIsIncorrectAnd'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
      return;
    }

    try {
      let reportResult = false;

      // Handle DingTalk environment
      if (isDingTalk() && productIdFromUrl) {
        const res = await reportFakeProduct({
          originProductId: productIdFromUrl,
          productId: `${productId}`,
        });
        reportResult = Boolean(res);
      } else if (productIdFromConfig) {
        // Handle web environment
        const res = await reportProduct({
          originProductId: productIdFromConfig,
          productId: `${productId}`,
        });
        reportResult = Boolean(res?.data?.data);
      }

      // Handle success/failure
      if (reportResult) {
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportSuccessful'),
          maskClickable: true,
          duration: 2,
          position: 'top',
        });
        // Add product to reported list to apply gray overlay and disable clicks
        setReportedProducts((prevReported) => new Set(prevReported).add(productId));
      } else {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFailedPleaseTryAgain'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_UnknownError');

      Toast.fail({
        content: errorMessage || i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFailedPleaseTryAgain'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }
  };

  // Handle product audit action
  const handleProductAudit = async (product: IOriginProduct, status: number) => {
    try {
      const response = await productAudit({
        productId: productData.productId,
        product1688Id: `${product.productId}`,
        status,
      });

      // 操作后，需要修改 productData 的状态为已审核
      setProductData((prevProductData) => ({
        ...prevProductData,
        auditStatus: 1,
      }));

      if (response.success) {
        // Update local product status
        setList((prevList) =>
          prevList.map((item) => {
            if (item.productId === product.productId) {
              return {
                ...item,
                auditCode: status,
              };
            }
            return item;
          }));

        // Show success message
        const statusText = status === 1 ? i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pass') : i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotPassed');
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditSuccess', { status: statusText }),
          maskClickable: true,
          position: 'top',
          duration: 2,
        });

        // Send analytics
        sendUT('productOriginV2_product_audit', {
          platform,
          reportType: getUrlParam('reportType'),
          bizId,
          rankType,
          productId: product.productId,
          productTitle: product.titleTranslated || product.title,
          auditStatus: status,
        });
      } else {
        Toast.fail({
          content: response.message || i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditFailed'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      }
    } catch (error) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditFailed'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }
  };

  if (loading) {
    return <Skeleton />;
  }

  return (
    <div className="productOrigin-page-container">
      <div className="productOrigin-container">
        <div className="productOrigin">
          <div className="img-container">
            <OptimizedImage
              src={productData.image || ''}
              alt="zozotown"
              width={120}
              height={120}
              lazy
              progressive
              quality={85}
              onClick={handleImageClick}
            />
            {productData?.reviewable && (
              productData.auditStatus === 1 ? <CheckCircleFilled className="audit-status-badge audited" /> : <ErrorFilled className="audit-status-badge unaudited" />)
            }
          </div>
          <div className="discription-container">
            <div className="product-title">{decode(productData?.title) || title || ''}</div>
            <div className="product-detail-bottom">
              <div className="price">
                {productData?.platform ? platformPriceMap[productData?.platform as EPlatform] : i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_Price')}
                <span style={{ marginLeft: '4px' }}>
                  {productData.price || price}
                  <span style={{ verticalAlign: 'baseline', fontSize: '12px' }}>
                    {productData.priceUnit || i18next.t('j-dingtalk-web_pages_create-order_Currency')}
                  </span>
                </span>
              </div>
              <div className="tag-wrap">
                <div className="product-stats">
                  {
                  // eslint-disable-next-line max-len
                    rankType ?
                      rankTypeMap[rankType as keyof typeof rankTypeMap]?.map((item) => {
                        if (!productData[item.type]) {
                          return null;
                        }

                        return (
                          <span key={`${item.type}`} className="value">{`${item.name} ${
                            productData[item.type]}`
                          }
                          </span>);
                      }) :
                      null
                  }
                </div>
              </div>
              {productData?.storeName &&
              <div className="shopName">{decode(productData?.storeName || '')}</div>
              }
            </div>
          </div>
        </div>
        <Divider full style={{ marginTop: '8px' }} />
        <div className="product-detail">
          <div className="detail-content">
            <div className="item">
              <div className="discription">
                {i18next.t(
                  'j-agent-web_pages_productOriginV2_components_ProductPage_ProductEvaluation',
                )}
              </div>
              <div className="dis-content">
                <Rate
                  style={{ '--star-size': '12px' }}
                  readOnly
                  value={productData?.reviewAverageScore || 0}
                  allowHalf
                />


              </div>
            </div>
            <div className="item">
              <div className="discription">
                {i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_Favorites')}
              </div>
              <div className="dis-content">
                {productData.favNum ?
                  i18next.t(
                    'j-agent-web_pages_productOrigin_components_ProductPage_ProductdatafavnumPeople',
                    { productDataFavNum: productData.favNum },
                  ) :
                  i18next.t(
                    'j-agent-web_pages_productOrigin_components_ProductPage_NoCollection',
                  )}
              </div>
            </div>
            <div className="item">
              <div className="discription">
                {i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_ListSource')}
              </div>
              <div className="dis-content">
                {productRankings.find((item) => item.id === rankType)?.rankingName ||
                i18next.t(
                  'j-agent-web_pages_productOriginV2_components_ProductPage_NoListInformation',
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="listTitle">
        {i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_SimilarProducts')}
      </div>
      <div className="productOrigin-list">
        {list?.map((item, index) => {
          const isReported = reportedProducts.has(item.productId);
          return (
            <div
              key={item.productId || `product-${index}`}
              className={`productOrigin ${isReported ? 'reported' : ''}`}
            >
              <div className="img-container">
                <OptimizedImage
                  src={item.primaryImage || ''}
                  alt={i18next.t(
                    'j-dingtalk-web_pages_productOriginV2_components_ProductPage_ProductPortrait',
                  )}
                  width={120}
                  height={120}
                  lazy
                  progressive
                  quality={85}
                  onClick={() => openProductDetail(item)}
                />
                {/* Audit status badge - only show for users with review permission */}
                {productData?.reviewable && (() => {
                  let TagIcon = <ErrorFilled className="audit-status-badge unaudited" />;

                  if (item.auditCode === 1) {
                    TagIcon = <CheckCircleFilled className="audit-status-badge approved" />;
                  } else if (item.auditCode === -1) {
                    TagIcon = <CloseCircledFilled className="audit-status-badge rejected" />;
                  }

                  return TagIcon;
                })()}

                <WarningOutlined
                  className="warning-icon"
                  onClick={(e) => {
                    handleReportClick(e, item?.productId);
                  }}
                />
              </div>
              <div
                className="discription-container"
                onClick={() => openProductDetail(item)}
              >
                <div className="product-title">
                  {isChinese() ?
                    item.title :
                    item.titleTranslated || item.title}
                </div>
                <div className="product-detail-bottom">
                  <div className="price-title">
                    {i18next.t(
                      'j-agent-web_pages_productOriginV2_components_ProductPage_FactoryPrice',
                    )}
                  </div>
                  <div className="price">
                    <span>
                      {item.priceInfo?.price}
                      <span style={{ verticalAlign: 'baseline', fontSize: '12px' }}>{i18next.t('j-dingtalk-web_pages_create-order_Currency')}</span>
                    </span>
                  </div>
                  <div className="tag-wrap">
                    <div className="product-stats">
                      {originTagMap.map((_t) => {
                        if (!item[_t.type]) {
                          return null;
                        }
                        return (
                          <span key={`${_t.type}`} className="value">{`${_t.name}${item[_t.type]}${
                            _t.unit}`
                          }
                          </span>);
                      })}
                    </div>
                  </div>

                  {/* Audit Mask and Buttons - only show for users with audit permission */}
                  {productData?.reviewable &&
                  <div className="audit-mask">
                    <div className="audit-buttons">
                      <Button
                        type="secondary"
                        size="small"
                        className="audit-btn reject-btn"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleProductAudit(item, -1);
                        }}
                      >
                        {i18next.t('j-dingtalk-web_components_SwipeableProductCard_Reject')}
                      </Button>
                      <Button
                        type="primary"
                        size="small"
                        className="audit-btn approve-btn"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleProductAudit(item, 1);
                        }}
                      >
                        {i18next.t('j-dingtalk-web_components_SwipeableProductCard_Approve')}
                      </Button>
                    </div>
                  </div>
                  }
                </div>
              </div>
            </div>);
        })}
      </div>
    </div>);
};

export default ProductPage;
