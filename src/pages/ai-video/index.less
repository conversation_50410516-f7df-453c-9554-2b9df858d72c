.ai-video-page {
  min-height: 100vh;
  overflow: hidden;
  background:
    radial-gradient(ellipse 600px 500px at top right,
      transparent 0%,
      transparent 30%,
      rgba(20, 20, 20, 0.1) 35%,
      rgba(20, 20, 20, 0.3) 45%,
      rgba(20, 20, 20, 0.5) 55%,
      rgba(20, 20, 20, 0.7) 65%,
      rgba(20, 20, 20, 0.85) 75%,
      #141414 85%
    ),
    linear-gradient(180deg, #6B1538 10%, #2F1624 100%);
  backdrop-filter: blur(40px);

  // 添加模糊过渡效果的伪元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 70%;
    height: 60%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.15) 0%,
      rgba(107, 21, 56, 0.1) 20%,
      rgba(47, 22, 36, 0.08) 40%,
      rgba(47, 22, 36, 0.05) 60%,
      transparent 80%
    );
    filter: blur(50px);
    pointer-events: none;
    z-index: 1;
  }

  // 添加第二层更柔和的模糊效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 80%;
    height: 70%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.08) 0%,
      rgba(47, 22, 36, 0.05) 30%,
      rgba(20, 20, 20, 0.03) 50%,
      transparent 70%
    );
    filter: blur(80px);
    pointer-events: none;
    z-index: 0;
  }
}

// PC Layout Styles
.ai-video-pc-layout {
  display: flex;
  height: 100vh;
  margin: 0 auto;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

  .ai-video-left-panel {
    flex: 0 0 412px;
    height: 100vh;  
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    background-color: #1e1e1f;
    overflow-y: auto;
    overflow-x: hidden;

    > div {
      height: auto;
    }
  }

  .ai-video-right-panel {
    flex: 1;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;

    > div {
      height: auto;
    }

    .video-list {
      max-width: 560px;
    }

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .ai-video-page {
    overflow: auto;
    padding-top: calc(44px + env(safe-area-inset-top));
  }

  .ai-video-pc-layout {
    flex-direction: column;
    height: auto;
    max-width: none;
    box-shadow: none;

    .ai-video-left-panel {
      flex: none;
      height: auto;
      border: none;
      overflow-y: visible;

      > div {
        height: auto;
      }
    }

    .ai-video-right-panel {
      height: auto;
      overflow-y: visible;

      > div {
        height: auto;
      }
    }
  }
}
