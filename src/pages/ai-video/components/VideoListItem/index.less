.video-list-item {
  width: 100%;
  background: #1e1e1f;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  .video-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #333;

    .task-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .task-mode {
        background: #2a2a2b;
        color: rgba(255, 255, 255, 0.8);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
      }

      .video-count {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px;
      }
    }

    .group-status {
      .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;

        &.status-finish {
          background: rgba(82, 196, 26, 0.2);
          color: #52c41a;
        }

        &.status-processing {
          background: rgba(24, 144, 255, 0.2);
          color: #1890ff;
        }

        &.status-pending {
          background: rgba(250, 140, 22, 0.2);
          color: #fa8c16;
        }

        &.status-failed {
          background: rgba(255, 77, 79, 0.2);
          color: #ff4d4f;
        }
      }
    }
  }

  .video-grid {
    display: grid;
    gap: 12px;

    &.single {
      grid-template-columns: 1fr;
    }

    &.double {
      grid-template-columns: 1fr 1fr;
    }

    &.triple {
      grid-template-columns: 1fr 1fr;

      .video-grid-item:first-child {
        grid-column: 1 / -1;
      }

      .video-grid-item:nth-child(2) {
        grid-column: 1;
      }

      .video-grid-item:nth-child(3) {
        grid-column: 2;
      }
    }

    .video-grid-item {
      .grid-video-item {
        margin-bottom: 0;
      }
    }
  }

  // GIF 转换进度指示器样式
  .gif-progress-indicator {
    margin-bottom: 16px;
    padding: 12px;
    background: #2a2a2b;
    border-radius: 8px;
    border: 1px solid #333;

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .progress-text {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
      }
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background: #333;
      border-radius: 4px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: #1890ff;
        border-radius: 4px;
        transition: width 0.3s ease;
      }
    }
  }

  // 操作按钮样式
  .action-section.pc-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;

    &.mobile-layout {
      gap: 24px;
      padding: 16px 0;
    }

    .action-button {
      background-color: transparent;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      color: rgba(255, 255, 255, 0.9);
      font-size: 24px;

      &:hover {
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }

      &.loading {
        opacity: 0.6;
        cursor: not-allowed;
        svg {
          animation: spin 1s linear infinite;
        }
      }

      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
      }

      &.liked {
        color: #ffffff;
      }

      &.disliked {
        color: #ffffff;
      }
    }
  }

  .completion-time {
    text-align: right;
    color: rgba(255, 255, 255, 0.4);
    font-size: 16px;
    line-height: 22px;
    flex-shrink: 0;
    margin-left: 16px;
  }

  .completion-time.mobile-layout {
    text-align: left;
    margin-left: 0;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
