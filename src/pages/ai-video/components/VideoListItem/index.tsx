import React, { useState, useCallback } from 'react';
import { Toast, Modal, ActionSheet } from 'dingtalk-design-mobile';
import {
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  RefreshOutlined,
  DownloadAndSaveOutlined,
  DeleteOutlined,
  MergeOutlined,
} from '@ali/ding-icons';
import { i18next } from '@ali/dingtalk-i18n';
import { isMobileDevice } from '@/utils/jsapi';
import { formatCompletionTime } from '@/utils/util';
import { downloadVideo, downloadGif, downloadVideosBatch } from '@/utils/download';
import {
  generateGifByRequestId,
  checkGifStatusByRequestId,
  generateGif,
  checkGifStatus,
  rateVideo,
  removeVideo,
  reGenerateVideo,
} from '@/apis';
import type { GenerateVideoGifResponse } from '@/apis/video';
import { sendUT } from '@/utils/trace';
import VideoItem from '../VideoItem';
import VideoMergeModal from '../VideoMergeModal';
import './index.less';

interface VideoInfo {
  videoUuid?: string;
  videoUrl: string;
  imageUrl: string;
  gifUrl?: string;
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  duration: number;
  requestId?: string;
  createdAt?: string;
  status?: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoInfoList {
  createdAt: string;
  updatedAt: string;
  operator?: string;
  uuid: string;
  userRating?: string;
  mergedVideoKey?: string;
  markedVideoKey?: string;
  taskMode?: 'SINGLE' | 'MULTI';
  datas: VideoInfo[];
  status: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoListItemProps {
  videoInfoList: VideoInfoList;
  onRegenerate?: (videoInfoList: VideoInfoList) => void;
  className?: string;
  progressMap?: Record<string, number>;
  loadVideoList: () => void;
  onOptimizedVideoUpdate?: (
    regenerateResult: any,
    originalUuid: string)
  => Promise<void>;
}

const VideoListItem: React.FC<VideoListItemProps> = ({
  videoInfoList,
  onRegenerate,
  className = '',
  progressMap = {},
  loadVideoList,
  onOptimizedVideoUpdate,
}) => {
  const [isLiked, setIsLiked] = useState(videoInfoList.userRating === '1');
  const [isDisliked, setIsDisliked] = useState(videoInfoList.userRating === '-1');
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [gifProgress, setGifProgress] = useState(0);
  const [showMergeModal, setShowMergeModal] = useState(false);

  // Check if current device is mobile
  const isMobile = isMobileDevice();

  // 只显示前三个视频
  const displayVideos = videoInfoList.datas;

  // Handle like action for the whole list
  const handleLike = async () => {
    try {
      if (isLiked) {
        await rateVideo({ uuid: videoInfoList.uuid, rating: 0 });
        setIsLiked(false);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ThePraiseIsCanceled'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
      } else {
        sendUT('aigc_video_like', {
          device: isMobile ? 'mobile' : 'pc',
          uuid: videoInfoList.uuid,
        });

        await rateVideo({ uuid: videoInfoList.uuid, rating: 1 });
        setIsLiked(true);
        setIsDisliked(false);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ThumbsUpSuccessfully'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
      }
    } catch (error) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }
  };

  // Handle dislike action for the whole list
  const handleDislike = async () => {
    try {
      if (isDisliked) {
        await rateVideo({ uuid: videoInfoList.uuid, rating: 0 });
        setIsDisliked(false);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_TheCancellationPointIsStepped'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
      } else {
        sendUT('aigc_video_unlike', {
          device: isMobile ? 'mobile' : 'pc',
          uuid: videoInfoList.uuid,
        });

        await rateVideo({ uuid: videoInfoList.uuid, rating: -1 });
        setIsDisliked(true);
        setIsLiked(false);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ClickSuccessfully'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
      }
    } catch (error) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }
  };

  // Handle download for the whole list (download all finished videos)
  const handleDownload = useCallback(async () => {
    const finishedVideos = videoInfoList.datas.filter(video => video.status === 'finish' && video.videoUrl);

    // Send UT for all videos
    finishedVideos.forEach(video => {
      sendUT('aigc_video_download', {
        device: isMobile ? 'mobile' : 'pc',
        uuid: video.videoUuid,
        videoUrl: video.videoUrl,
      });
    });

    if (finishedVideos.length === 1) {
      // Single video download
      const video = finishedVideos[0];
      downloadVideo(video.videoUrl, video.videoUuid);
    } else {
      // Batch download multiple videos
      const videoList = finishedVideos.map(video => ({
        url: video.videoUrl,
        uuid: video.videoUuid,
      }));

      try {
        await downloadVideosBatch(videoList, (completed, total) => {
          console.log(`Downloaded ${completed}/${total} videos`);
        });
      } catch (error) {
        console.error('Batch download error:', error);
        Toast.fail({
          content: '批量下载失败',
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      }
    }
  }, [isMobile, videoInfoList.datas]);

  // Handle merge for the whole list
  const handleMerge = useCallback(() => {
    sendUT('aigc_video_merge', {
      device: isMobile ? 'mobile' : 'pc',
      uuid: videoInfoList.uuid,
    });

    setShowMergeModal(true);
  }, [isMobile, videoInfoList.uuid]);

  // Handle merge operation from modal
  const handleMergeConfirm = useCallback(async (selectedVideos: VideoInfo[], addWatermark: boolean) => {
    // The actual merge logic is now handled in VideoMergeModal component
    // This callback is kept for future extensibility if needed
    console.log('Merge operation started with:', {
      selectedVideos: selectedVideos.length,
      addWatermark,
    });
  }, []);

  // Handle modal close
  const handleMergeModalClose = useCallback(() => {
    setShowMergeModal(false);
  }, []);

  // Handle regenerate for the whole list
  const handleRegenerateAll = async () => {
    sendUT('aigc_video_regenerate_all', {
      device: isMobile ? 'mobile' : 'pc',
      uuid: videoInfoList.uuid,
    });

    // For other cases, use the original callback behavior
    if (onRegenerate) {
      onRegenerate(videoInfoList);
    }
  };

  // Optimized video update handler after regeneration
  const handleOptimizedVideoUpdate = async (regenerateResult: any, originalUuid: string) => {
    // Use the optimized update function from parent if available, otherwise fallback
    if (onOptimizedVideoUpdate) {
      await onOptimizedVideoUpdate(regenerateResult, originalUuid);
    } else {
      // Fallback to original behavior
      loadVideoList();
    }
  };

  // Handle regenerate for a single video
  const handleRegenerateSingle = async (videoInfo: VideoInfo) => {

    sendUT('aigc_video_regenerate_single', {
      device: isMobile ? 'mobile' : 'pc',
      uuid: videoInfo.videoUuid,
    });

    if (videoInfo.status === 'failed') {
      if (isRegenerating) return;

      setIsRegenerating(true);

      try {
        Toast.info({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegeneratingVideo'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });

        const result = await reGenerateVideo({
          fromUuid: videoInfo.videoUuid || videoInfoList.uuid,
          requestId: videoInfo.requestId,
        }) as any;

        if (result?.success) {
          Toast.success({
            content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationHasStartedPleaseWait'),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });

          // Optimized: Use targeted status check instead of full list reload
          await handleOptimizedVideoUpdate(result, videoInfo.videoUuid || videoInfoList.uuid);
        } else {
          throw new Error(result?.errorMsg || i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailed'));
        }
      } catch (error) {
        Toast.fail({
          content: error instanceof Error ? error?.message : i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailedPleaseTryAgain'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      } finally {
        setIsRegenerating(false);
      }
      return;
    }

    // For other cases, use the original callback behavior
    if (onRegenerate) {
      onRegenerate({ ...videoInfoList, datas: [videoInfo] });
    }
  };

  // Handle remove for the whole list
  const handleRemove = async () => {
    Modal.alert(
      i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConfirmDeletion'),
      i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_AreYouSureYouWant'),
      [
        {
          text: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Cancel'),
          onClick: () => {},
        },
        {
          text: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Delete'),
          style: { color: '#FF0E53' },
          onClick: async () => {
            try {
              const result = await removeVideo({ uuid: videoInfoList.uuid }) as any;
              if (result?.success) {
                Toast.success({
                  content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_DeletedSuccessfully'),
                  duration: 2,
                  position: 'top',
                  maskClickable: true,
                });
                loadVideoList();
              } else {
                Toast.fail({
                  content: result?.errorMsg || i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry'),
                  duration: 3,
                  position: 'top',
                  maskClickable: true,
                });
              }
            } catch (error) {
              Toast.fail({
                content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry'),
                duration: 3,
                position: 'top',
                maskClickable: true,
              });
            }
          },
        },
      ]
    );
  };

  // Generate menu items based on video status and conversion state
  const generateMenuItems = useCallback((videoInfo: VideoInfo, isConverting: boolean, gifProgress: number) => {
    const menuItems = [
      {
        value: '重新生成',
        disabled: false,
      },
    ];

    // Only show download option if video is finished and has videoUrl
    if (videoInfo.status === 'finish' && videoInfo.videoUrl) {
      menuItems.push({
        value: i18next.t('j-dingtalk-web_pages_ai-image_components_ImagePreview_Download'),
        disabled: false,
      });
    }

    // Generate GIF menu item with conditional text
    const getGifMenuText = () => {
      if (videoInfo.gifUrl) {
        return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_DownloadGif');
      }
      if (isConverting && gifProgress > 0) {
        return `${i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif')} ${Math.round(gifProgress)}%`;
      }
      if (isConverting) {
        return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif');
      }
      return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif');
    };

    menuItems.push({
      value: getGifMenuText(),
      disabled: isConverting,
    });

    return menuItems;
  }, [gifProgress]);

  // Handle video menu click
  const handleVideoMenuClick = useCallback((videoInfo: VideoInfo) => {
    // Generate menu items using the helper function
    const menuItems = generateMenuItems(videoInfo, isConverting, gifProgress);

    // 使用 ActionSheet.show API
    ActionSheet.show({
      title: '',
      items: menuItems,
      cancelText: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Cancel'),
      maskClosable: true,
    }, (res) => {
      const itemIndex = res.index;
      let actionIndex = 0;

      // 重新生成
      if (itemIndex === actionIndex) {
        if (videoInfo) {
          handleRegenerateSingle(videoInfo);
        }
        return;
      }
      actionIndex++;

      // 下载 (only if available)
      if (videoInfo.status === 'finish' && videoInfo.videoUrl) {
        if (itemIndex === actionIndex) {
          if (videoInfo && videoInfo.videoUrl) {
            downloadVideo(videoInfo.videoUrl, videoInfo.videoUuid);
          }
          return;
        }
        actionIndex++;
      }

      // 转换为GIF
      if (itemIndex === actionIndex) {
        if (videoInfo) {
          handleConvertToGif(videoInfo);
        }
      }
    });
  }, [isConverting, progressMap, generateMenuItems, gifProgress, onRegenerate]);

  // Handle convert to GIF for selected video
  const handleConvertToGif = async (video: VideoInfo) => {
    if (isConverting) return;

    if (video.gifUrl) {
      Toast.success({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifAlreadyExistsStartDownloading'),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
      downloadGif(video.gifUrl, video.videoUuid);
      return;
    }

    setIsConverting(true);
    setGifProgress(5); // 设置初始进度，让进度条立即显示

    try {
      // 根据 taskMode 选择不同的接口
      const generateResult = videoInfoList.taskMode === 'MULTI'
        ? await generateGifByRequestId({
            uuid: video.videoUuid,
            requestId: video.requestId,
          }) as GenerateVideoGifResponse
        : await generateGif({
            uuid: video.videoUuid,
            requestId: video.requestId,
          }) as GenerateVideoGifResponse;

      if ((generateResult.status === 'completed' || generateResult.status === 'finish') && generateResult.gifUrl) {
        setGifProgress(100);
        downloadGif(generateResult.gifUrl, video.videoUuid);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationCompleted'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
        loadVideoList();
      }

      if (generateResult.status !== 'processing') {
        throw new Error(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedToStart'));
      }

      Toast.success({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationHasStartedPlease'),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });

      // Step 2: Start polling for GIF status
      await pollGifStatus(video.videoUuid, video.requestId, videoInfoList.taskMode);
    } catch (error) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedPleaseTry'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    } finally {
      setIsConverting(false);
      setGifProgress(0);
    }
  };

  // Poll GIF generation status
  const pollGifStatus = async (videoUuid: string, requestId: string, taskMode?: string) => {
    let pollCount = 0;
    const maxPollCount = 50;
    const pollInterval = 3000;

    const poll = async (): Promise<void> => {
      try {
        pollCount++;

        const progressPercentage = Math.min(pollCount / maxPollCount * 90, 90);
        setGifProgress(progressPercentage);

        // 根据 taskMode 选择不同的接口
        const result = taskMode === 'MULTI'
          ? await checkGifStatusByRequestId({ uuid: videoUuid, requestId })
          : await checkGifStatus({ uuid: videoUuid });

        if (result.status === 'completed' && result.gifUrl) {
          setGifProgress(100);

          downloadGif(result.gifUrl, videoUuid);

          Toast.success({
            content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationCompleted'),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });
          setIsConverting(false);
          setGifProgress(0);

          loadVideoList();
          return;
        }

        if (result.status === 'failed') {
          throw new Error(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailed'));
        }

        if (result.status === 'processing') {
          if (pollCount >= maxPollCount) {
            throw new Error(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationTimeout'));
          }

          setTimeout(() => {
            poll();
          }, pollInterval);
          return;
        }

        throw new Error(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_UnknownStatusResultstatus', { resultStatus: result.status }));
      } catch (error) {
        Toast.fail({
          content: error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedPleaseTry'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
        setIsConverting(false);
        setGifProgress(0);
      }
    };

    // Start the first poll
    poll();
  };

  return (
    <>
      <div className={`video-list-item ${className}`}>
      {/* 视频组头部信息 */}
      <div className="video-group-header">
        <div className="task-info">
          <span className="task-mode">
            {videoInfoList.taskMode === 'MULTI' ? '批量任务' : '单个任务'}
          </span>
          <span className="video-count">
            {videoInfoList.datas.length} 个视频
          </span>
        </div>
        <div className="group-status">
          <span className={`status-badge status-${videoInfoList.status}`}>
            {videoInfoList.status === 'finish' && '已完成'}
            {videoInfoList.status === 'processing' && '生成中'}
            {videoInfoList.status === 'pending' && '等待中'}
            {videoInfoList.status === 'failed' && '失败'}
          </span>
        </div>
      </div>

      {/* GIF 转换进度指示器 */}
      {isConverting && (
        <div className="gif-progress-indicator">
          <div className="progress-info">
            <span className="progress-text">
              {gifProgress > 0
                ? `${i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif')} ${Math.round(gifProgress)}%`
                : i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif')
              }
            </span>
          </div>
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${Math.max(gifProgress, 5)}%` }} // 至少显示5%的进度条
            />
          </div>
        </div>
      )}

      {/* 视频网格展示 */}
      <div className={`video-grid ${displayVideos.length === 1 ? 'single' : displayVideos.length === 2 ? 'double' : 'triple'}`}>
        {displayVideos.map((videoInfo, index) => (
          <div key={`${videoInfoList.uuid}-${videoInfo.videoUuid}-${index}`} className="video-grid-item">
            <VideoItem
              videoInfo={videoInfo}
              className="grid-video-item"
              progress={progressMap[videoInfo.videoUuid] || 0}
              onVideoMenuClick={handleVideoMenuClick}
            />
          </div>
        ))}
      </div>

      {/* 整体操作按钮 - 仅在有完成或失败的视频时显示 */}
      {(videoInfoList.status === 'finish' || videoInfoList.status === 'failed') && (
        <>
          {/* PC Layout: Action buttons and completion time in same row */}
          {!isMobile && (
            <div className="action-section pc-layout">
              <div className="action-buttons">
                {videoInfoList.status === 'finish' && (
                  <>
                    <button className={`action-button ${isLiked ? 'liked' : ''}`} onClick={handleLike}>
                      {isLiked ? <LikeFilled /> : <LikeOutlined />}
                    </button>
                    <button className={`action-button ${isDisliked ? 'disliked' : ''}`} onClick={handleDislike}>
                      {isDisliked ? <DislikeFilled /> : <DislikeOutlined />}
                    </button>
                    {onRegenerate && (
                      <button className="action-button" onClick={handleRegenerateAll}>
                        <RefreshOutlined />
                      </button>
                    )}
                    {
                      videoInfoList.datas?.some(v => v.videoUrl) && (
                        <>
                          <button className="action-button" onClick={handleDownload}>
                            <DownloadAndSaveOutlined />
                          </button>
                          {
                            videoInfoList.datas?.map(v => v.videoUrl)?.length > 1 && (
                              <button className="action-button" onClick={handleMerge}>
                                <MergeOutlined />
                              </button>
                            )
                          }
                        </>
                      )
                    }
                  </>
                )}
                {videoInfoList.status === 'failed' && (
                  <button
                    className={`action-button ${isRegenerating ? 'loading' : ''}`}
                    onClick={handleRegenerateAll}
                    disabled={isRegenerating}
                  >
                    <RefreshOutlined />
                  </button>
                )}
                <button className="action-button" onClick={handleRemove}>
                  <DeleteOutlined />
                </button>
              </div>
              {(videoInfoList.status === 'finish' || videoInfoList.status === 'failed') && videoInfoList.updatedAt && (
                <div className="completion-time">
                  {formatCompletionTime(new Date(videoInfoList.updatedAt))}
                </div>
              )}
            </div>
          )}

          {/* Mobile Layout: Action buttons and completion time in separate rows */}
          {isMobile && (
            <>
              <div className="action-buttons mobile-layout">
                {videoInfoList.status === 'finish' && (
                  <>
                    <button className={`action-button ${isLiked ? 'liked' : ''}`} onClick={handleLike}>
                      {isLiked ? <LikeFilled /> : <LikeOutlined />}
                    </button>
                    <button className={`action-button ${isDisliked ? 'disliked' : ''}`} onClick={handleDislike}>
                      {isDisliked ? <DislikeFilled /> : <DislikeOutlined />}
                    </button>
                    {onRegenerate && (
                      <button className="action-button" onClick={handleRegenerateAll}>
                        <RefreshOutlined />
                      </button>
                    )}
                    {
                      videoInfoList.datas?.some(v => v.videoUrl) && (
                        <>
                          <button className="action-button" onClick={handleDownload}>
                            <DownloadAndSaveOutlined />
                          </button>
                          {
                            videoInfoList.datas?.map(v => v.videoUrl)?.length > 1 && (
                              <button className="action-button" onClick={handleMerge}>
                                <MergeOutlined />
                              </button>
                            )
                          }
                        </>
                      )
                    }
                  </>
                )}
                {videoInfoList.status === 'failed' && (
                  <button
                    className={`action-button ${isRegenerating ? 'loading' : ''}`}
                    onClick={handleRegenerateAll}
                    disabled={isRegenerating}
                  >
                    <RefreshOutlined />
                  </button>
                )}
                <button className="action-button" onClick={handleRemove}>
                  <DeleteOutlined />
                </button>
              </div>
              {(videoInfoList.status === 'finish' || videoInfoList.status === 'failed') && videoInfoList.updatedAt && (
                <div className="completion-time mobile-layout">
                  {formatCompletionTime(new Date(videoInfoList.updatedAt))}
                </div>
              )}
            </>
          )}
        </>
      )}

      {/* Video Merge Modal */}
      <VideoMergeModal
        visible={showMergeModal}
        onClose={handleMergeModalClose}
        videoInfoList={videoInfoList}
        onMerge={handleMergeConfirm}
        onVideoListUpdate={loadVideoList}
      />
    </div>
    </>
  );
};

export default React.memo(VideoListItem);
