import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useRef, useCallback } from 'react';
import { Button } from 'dingtalk-design-mobile';
import {
  RefreshOutlined,
  // EditOutlined,
} from '@ali/ding-icons';
import {
  isMobileDevice,
  // isDingTalk,
} from '@/utils/jsapi';
import VideoListItem from '../VideoListItem';
import './index.less';

interface VideoInfo {
  videoUuid?: string;
  videoUrl: string;
  imageUrl: string;
  gifUrl?: string;
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  duration: number;
  userRating?: string;
  requestId?: string;
  status?: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoInfoList {
  createdAt: string;
  updatedAt: string;
  operator?: string;
  uuid: string;
  userRating?: string;
  mergedVideoKey?: string;
  markedVideoKey?: string;
  taskMode?: 'SINGLE' | 'MULTI';
  datas: VideoInfo[];
  status: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoListProps {
  videoList: VideoInfoList[]; // 改为 VideoInfoList 数组
  isLoading: boolean;
  error: string | null;
  onCreateNew: () => void;
  onRegenerate?: (videoInfoList: VideoInfoList) => void;
  onRefresh?: () => void;
  className?: string;
  progressMap?: Record<string, number>; // Map of video UUID to progress percentage
  hasNextPage?: boolean; // Whether there are more pages to load
  isLoadingMore?: boolean; // Loading state for pagination
  onLoadMore?: () => void; // Function to load more videos
  loadVideoList: () => void; // Load video list
  onOptimizedVideoUpdate?: (
    regenerateResult: any,
    originalUuid: string)
  => Promise<void>; // Optimized video update
}

const VideoList: React.FC<VideoListProps> = ({
  videoList,
  isLoading,
  error,
  onCreateNew,
  onRegenerate,
  onRefresh,
  className = '',
  progressMap = {},
  hasNextPage = false,
  isLoadingMore = false,
  onLoadMore,
  loadVideoList,
  onOptimizedVideoUpdate,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<number | null>(null);

  // Handle scroll event for infinite loading with optimized throttling
  const handleScrollThrottled = useCallback(() => {
    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
      scrollTimeoutRef.current = null;
    }

    scrollTimeoutRef.current = window.setTimeout(() => {
      // Check if component is still mounted
      if (!containerRef.current) return;

      if (!hasNextPage || isLoadingMore || !onLoadMore) {
        return;
      }

      let scrollTop: number;
      let scrollHeight: number;
      let clientHeight: number;

      if (isMobileDevice()) {
        // Mobile: use window scroll
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.documentElement.scrollHeight;
        clientHeight = window.innerHeight;
      } else {
        // PC: use right panel scroll (parent container)
        const rightPanel = containerRef.current?.closest('.ai-video-right-panel') as HTMLElement;
        if (!rightPanel) return;

        scrollTop = rightPanel.scrollTop;
        scrollHeight = rightPanel.scrollHeight;
        clientHeight = rightPanel.clientHeight;
      }

      // Trigger load more when scrolled to 80% of the content
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      if (scrollPercentage >= 0.8) {
        onLoadMore();
      }
    }, 200);
  }, [hasNextPage, isLoadingMore, onLoadMore]);

  // Add scroll listener with proper cleanup
  useEffect(() => {
    let scrollElement: HTMLElement | Window | null = null;

    if (isMobileDevice()) {
      // Mobile: listen to window scroll
      scrollElement = window;
      window.addEventListener('scroll', handleScrollThrottled, { passive: true });
    } else {
      // PC: listen to right panel scroll
      const rightPanel = containerRef.current?.closest('.ai-video-right-panel') as HTMLElement;
      if (rightPanel) {
        scrollElement = rightPanel;
        rightPanel.addEventListener('scroll', handleScrollThrottled, { passive: true });
      }
    }

    return () => {
      // Cleanup scroll listener
      if (scrollElement) {
        if (scrollElement === window) {
          window.removeEventListener('scroll', handleScrollThrottled);
        } else {
          (scrollElement as HTMLElement).removeEventListener('scroll', handleScrollThrottled);
        }
      }

      // Cleanup timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = null;
      }
    };
  }, [handleScrollThrottled]);

  // Additional cleanup on unmount - redundant but safe
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = null;
      }
    };
  }, []);

  // Render empty state
  const renderEmptyState = () =>
    (
      <div className="video-list-empty">
        <div className="empty-icon">🎬</div>
        <div className="empty-title">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_NoVideoAvailable')}</div>
        <div className="empty-description">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_StartCreatingYourFirstAi')}
        </div>
        <Button
          type="primary"
          size="large"
          className="create-first-video-btn"
          onClick={onCreateNew}
        >
          {i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_CreateAVideo')}
        </Button>
      </div>
    );


  // Render error state
  const renderErrorState = () =>
    (
      <div className="video-list-error">
        <div className="error-icon">❌</div>
        <div className="error-title">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToLoad')}</div>
        <div className="error-description">
          {error || i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToObtainTheVideo')}
        </div>
        <div className="error-actions">
          {onRefresh &&
          <Button
            type="secondary"
            size="middle"
            onClick={onRefresh}
            className="retry-btn"
          >
            <RefreshOutlined />{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_Retry')}
          </Button>
          }
          <Button
            type="primary"
            size="middle"
            onClick={onCreateNew}
            className="create-new-btn"
          >
            {i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_CreateANewVideo')}
          </Button>
        </div>
      </div>
    );

  // Render loading state
  const renderLoadingState = () =>
    (
      <div className="video-list-loading">
        <div className="loading-spinner" />
        <div className="loading-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingVideoList')}</div>
      </div>
    );

  // Render video list content
  const renderVideoList = () =>
    (
      <div className="video-list-content">
        {/* Video list */}
        <div className="video-list-items" ref={containerRef}>
          {videoList.map((videoInfoList) =>
            (<VideoListItem
              key={videoInfoList.uuid}
              videoInfoList={videoInfoList}
              onRegenerate={onRegenerate}
              loadVideoList={loadVideoList}
              onOptimizedVideoUpdate={onOptimizedVideoUpdate}
              className="video-list-item"
              progressMap={progressMap}
            />))}
        </div>

        {/* Load more section with Intersection Observer sentinel */}
        {videoList.length > 0 &&
        <div className="load-more-section">
          {/* Removed sentinel element - using scroll-based loading instead */}

          {isLoadingMore &&
          <div className="load-more-loading">
            <div className="loading-spinner" />
            <div className="loading-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingMore')}</div>
          </div>
          }
          {!isLoadingMore && !hasNextPage &&
          <div className="load-more-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_AllVideosAreDisplayed')}
          </div>
          }
          {!isLoadingMore && hasNextPage &&
          <div className="load-more-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_SlideDownToLoadMore')}
          </div>
          }
        </div>
        }
      </div>
    );

  return (
    <div className={`video-list ${className}`}>
      {isLoading && renderLoadingState()}
      {!isLoading && error && renderErrorState()}
      {!isLoading && !error && videoList.length === 0 && renderEmptyState()}
      {!isLoading && !error && videoList.length > 0 && renderVideoList()}
    </div>);
};

export default React.memo(VideoList);
