import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { Modal, Checkbox, Switch, Button, Toast } from 'dingtalk-design-mobile';
import { CloseOutlined, DragOutlined } from '@ali/ding-icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { sendUT } from '@/utils/trace';
import { isMobileDevice } from '@/utils/jsapi';
import { optimizeVideoPosterUrl } from '@/utils/imageOptimization';
import { mergeVideo, checkMergeVideoStatus, markMergedVideo, checkMarkVideoStatus, generateOpenUrl } from '@/apis';
import type { MergeVideoRequest, CheckMergeVideoStatusResponse, CheckMarkVideoStatusResponse } from '@/apis/video';
import { usePolling } from '@/hooks/usePolling';
// import { downloadVideo } from '@/utils/download';
import './index.less';

interface VideoInfo {
  videoUuid?: string;
  videoUrl: string;
  imageUrl: string;
  gifUrl?: string;
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  duration: number;
  requestId?: string;
  createdAt?: string;
  status?: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoMergeModalProps {
  visible: boolean;
  onClose: () => void;
  videoInfoList: {
    datas: VideoInfo[];
    uuid: string;
    mergedVideoKey?: string;
    markedVideoKey?: string;
  };
  onMerge: (selectedVideos: VideoInfo[], addWatermark: boolean) => void;
  onVideoListUpdate?: () => void;
}

const VideoMergeModal: React.FC<VideoMergeModalProps> = ({
  visible,
  onClose,
  videoInfoList,
  onMerge,
  onVideoListUpdate,
}) => {
  const [selectedVideos, setSelectedVideos] = useState<string[]>([]);
  const [addWatermark, setAddWatermark] = useState(false);
  const [isMerging, setIsMerging] = useState(false);
  const [mergeProgress, setMergeProgress] = useState(0);
  const [isAddingWatermark, setIsAddingWatermark] = useState(false);
  const [watermarkProgress, setWatermarkProgress] = useState(0);

  // Drag and drop state
  const [videos, setVideos] = useState<VideoInfo[]>([]);

  const isMobile = isMobileDevice();

  // Polling hooks for merge and watermark operations
  const mergePolling = usePolling<CheckMergeVideoStatusResponse>({
    maxAttempts: 30, // Increase max attempts to 60 (60 seconds total)
    interval: 1000,
    checkFunction: async () => {
      if (!mergeTaskIdRef.current || !videoUuidRef.current) {
        throw new Error('缺少必要的任务信息');
      }
      return await checkMergeVideoStatus({
        taskId: mergeTaskIdRef.current,
        uuid: videoUuidRef.current,
      });
    },
    shouldContinue: (result) => result.status === 'pending' || result.status === 'processing',
    isSuccess: (result) => result.status === 'finish' && result.success,
    isFailed: (result) => result.status === 'failed',
    getErrorMessage: (result) => result.errorMsg || '视频合成失败',
    onStart: () => {
      console.log('开始轮询视频合成状态...');
      setMergeProgress(0);
    },
    onSuccess: async (result) => {
      console.log('视频合成完成:', result);
      setIsMerging(false);

      // 清除视频合成轮询的定时器，确保没有残留的定时器
      mergePolling.cancelPolling();

      if (addWatermark) {
        await startWatermarkProcess(result.url, result.videoKey);
      } else {
        await handleDownloadResult(result.url);
      }
    },
    onError: (error) => {
      console.error('视频合成失败:', error);

      // 清除视频合成轮询的定时器
      mergePolling.cancelPolling();

      // Provide more specific error message for timeout issues
      const errorMessage = error.includes('SEND_TIMEOUT')
        ? '网络连接超时，请检查网络后重试'
        : error;

      Toast.fail({
        content: errorMessage,
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
      setIsMerging(false);
      setMergeProgress(0);
    },
    onProgress: (attempt, maxAttempts) => {
      const progress = Math.min(Math.round((attempt / maxAttempts) * 100), 95);
      setMergeProgress(progress);
    },
  });

  const watermarkPolling = usePolling<CheckMarkVideoStatusResponse>({
    maxAttempts: 60, // Increase max attempts to 60 (60 seconds total)
    interval: 1000,
    checkFunction: async () => {
      if (!watermarkTaskIdRef.current || !videoUuidRef.current) {
        throw new Error('缺少必要的水印任务信息');
      }
      // Use longer timeout for watermark status check (20 seconds)
      return await checkMarkVideoStatus({
        taskId: watermarkTaskIdRef.current,
        uuid: videoUuidRef.current,
        requestId: watermarkRequestIdRef.current || '',
      }, 20000); // 20 seconds timeout
    },
    shouldContinue: (result) => result.status === 'pending' || result.status === 'processing',
    isSuccess: (result) => result.status === 'finish' && result.success,
    isFailed: (result) => result.status === 'failed',
    getErrorMessage: (result) => result.errorMsg || '添加水印失败',
    onStart: () => {
      console.log('开始轮询水印处理状态...');
      setIsAddingWatermark(true);
      setWatermarkProgress(0);
    },
    onSuccess: async (result) => {
      console.log('水印添加完成:', result);

      // 清除水印轮询的定时器
      watermarkPolling.cancelPolling();

      await handleDownloadResult(result.url);
    },
    onError: (error) => {
      console.error('水印添加失败:', error);

      // 清除水印轮询的定时器
      watermarkPolling.cancelPolling();

      // Provide more specific error message for timeout issues
      const errorMessage = error.includes('SEND_TIMEOUT')
        ? '网络连接超时，请检查网络后重试'
        : error;

      Toast.fail({
        content: errorMessage,
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
      setIsAddingWatermark(false);
      setWatermarkProgress(0);
    },
    onProgress: (attempt, maxAttempts) => {
      const progress = Math.min(Math.round((attempt / maxAttempts) * 100), 95);
      setWatermarkProgress(progress);
    },
  });

  // Refs to store task IDs across polling sessions
  const mergeTaskIdRef = useRef<string>('');
  const videoUuidRef = useRef<string>('');
  const watermarkTaskIdRef = useRef<string>('');
  const watermarkRequestIdRef = useRef<string>('');

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Filter videos that are finished and have videoUrl
  const availableVideos = useMemo(() => {
    return videoInfoList.datas?.filter(video => video.videoUrl);
  }, [videoInfoList.datas]);

  // Sync videos with availableVideos
  useEffect(() => {
    setVideos(availableVideos);
  }, [availableVideos]);

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setVideos((items) => {
        const oldIndex = items.findIndex(item => item.requestId === active.id);
        const newIndex = items.findIndex(item => item.requestId === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  }, []);

  // Handle video selection
  const handleVideoSelect = useCallback((videoUuid: string, checked: boolean) => {
    setSelectedVideos(prev => {
      if (checked) {
        return [...prev, videoUuid];
      } else {
        return prev.filter(uuid => uuid !== videoUuid);
      }
    });
  }, []);

  // Handle select all - intelligent toggle behavior
  const handleSelectAll = useCallback(() => {
    const currentlyAllSelected = selectedVideos.length === availableVideos.length &&
                                 availableVideos.every(video => selectedVideos.includes(video.requestId || ''));

    if (currentlyAllSelected) {
      setSelectedVideos([]);
    } else {
      const allVideoIds = availableVideos.map(video => video.requestId || '');
      setSelectedVideos(allVideoIds);
    }
  }, [availableVideos, selectedVideos]);

  const handleClose = useCallback(() => {
    setSelectedVideos([]);
    setAddWatermark(false);
    setIsMerging(false);
    setMergeProgress(0);
    setIsAddingWatermark(false);
    setWatermarkProgress(0);

    // Cancel any ongoing polling
    mergePolling.cancelPolling();
    watermarkPolling.cancelPolling();

    // Reset refs
    mergeTaskIdRef.current = '';
    videoUuidRef.current = '';
    watermarkTaskIdRef.current = '';
    watermarkRequestIdRef.current = '';

    onClose();
  }, [onClose, mergePolling, watermarkPolling]);

  // Handle merge button click
  const handleMergeClick = useCallback(async () => {
    if (selectedVideos.length === 0) {
      Toast.fail({
        content: '请先选择要合成的视频',
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
      return;
    }

    if (selectedVideos.length < 2) {
      Toast.fail({
        content: '至少需要选择2个视频进行合成',
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
      return;
    }

    setIsMerging(true);
    setMergeProgress(0);

    try {
      // Get selected videos in the order they appear in the sorted videos list
      const selectedVideoObjects = videos.filter(video =>
        selectedVideos.includes(video.requestId || '')
      );

      // Send UT tracking
      sendUT('aigc_video_merge_start', {
        device: isMobile ? 'mobile' : 'pc',
        videoCount: selectedVideos.length,
        addWatermark,
      });

      // Call merge API with sorted requestIds
      const mergeRequest: MergeVideoRequest = {
        uuid: videoInfoList.uuid,
        requestIds: selectedVideoObjects.map(video => video.requestId || ''),
        addMark: addWatermark,
      };

      const mergeResult = await mergeVideo(mergeRequest);

      if (mergeResult.success) {
        Toast.success({
          content: `开始合成 ${selectedVideos.length} 个视频${addWatermark ? '（带水印）' : ''}`,
          duration: 2,
          position: 'top',
          maskClickable: true,
        });

        // Store merge task info
        mergeTaskIdRef.current = mergeResult.taskId;
        videoUuidRef.current = videoInfoList.uuid || '';

        // Start polling for merge status
        await mergePolling.startPolling();
      } else {
        throw new Error(mergeResult.errorMsg || '合成失败');
      }
    } catch (error) {
      console.error('Video merge failed:', error);
      Toast.fail({
        content: error instanceof Error ? error.message : '合成失败，请重试',
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
      setIsMerging(false);
    }
  }, [selectedVideos, addWatermark, videos, onMerge, isMobile]);

  // Start watermark process
  const startWatermarkProcess = useCallback(async (mergedVideoUrl: string, videoKey: string) => {
    try {
      console.log('开始添加水印，合成视频URL:', mergedVideoUrl);

      // Reset watermark progress before starting
      setWatermarkProgress(0);

      const markRequest = {
        uuid: videoUuidRef.current,
        url: mergedVideoUrl,
      };

      const markResult = await markMergedVideo(markRequest);

      if (markResult.success) {
        // Store watermark task info
        watermarkTaskIdRef.current = markResult.taskId;
        watermarkRequestIdRef.current = videoKey;

        // Start polling watermark status
        await watermarkPolling.startPolling();
      } else {
        throw new Error(markResult.errorMsg || '启动水印添加失败');
      }
    } catch (error) {
      console.error('启动水印添加失败:', error);
      const errorMessage = error instanceof Error ? error.message : '启动水印添加失败';
      Toast.fail({
        content: errorMessage,
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
      setIsAddingWatermark(false);
    }
  }, [watermarkPolling]);

  // Handle download result (either merged video or watermarked video)
  const handleDownloadResult = useCallback(async (videoUrl: string) => {
    try {
      setIsMerging(false);
      setIsAddingWatermark(false);
      setMergeProgress(0);
      setWatermarkProgress(0);

      // Update video list if callback provided
      if (onVideoListUpdate) {
        onVideoListUpdate();
      }

      // Show success message
      const successMessage = addWatermark ? '视频合成并添加水印完成' : '视频合成完成';
      Toast.success({
        content: successMessage,
        duration: 2,
        position: 'top',
        maskClickable: true,
      });

      // Open download URL
      if (videoUrl) {
        window.open(videoUrl, '_blank');
      }
    } catch (error) {
      console.error('处理下载结果失败:', error);
      Toast.fail({
        content: '处理结果失败',
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
    }
  }, [addWatermark, onVideoListUpdate, handleClose]);

  // Handle download
  const handleDownload = useCallback(async () => {
    try {
      let videoKey: string | undefined;
      if (videoInfoList.mergedVideoKey) {
        videoKey = videoInfoList.mergedVideoKey;
      } else if (videoInfoList.markedVideoKey) {
        videoKey = videoInfoList.markedVideoKey;
      }

      if (!videoKey) {
        Toast.fail({
          content: '没有可下载的视频',
          duration: 2,
        });
        return;
      }

      const response = await generateOpenUrl({ fileName: videoKey });
      if (!response.success || !response.url) {
        Toast.fail({
          content: response.errorMsg || '获取下载链接失败',
          duration: 2,
        });
        return;
      }

      window.open(response.url, '_blank');
      // await downloadVideo(response.url, videoInfoList.uuid);
    } catch (error) {
      console.error('Download failed:', error);
      Toast.fail({
        content: '下载失败，请重试',
        duration: 2,
      });
    }
  }, [videoInfoList.mergedVideoKey, videoInfoList.markedVideoKey, videoInfoList.uuid]);

  const selectedCount = selectedVideos.length;
  const allSelected = availableVideos.length > 0 && selectedVideos.length === availableVideos.length;

  return (
    <Modal
      title={
        <div className="modal-title-with-close">
          <span>视频合成</span>
          <button
            className="close-button"
            onClick={handleClose}
            disabled={isMerging}
          >
            <CloseOutlined />
          </button>
        </div>
      }
      visible={visible}
      onClose={handleClose}
      maskClosable={false}
      className="video-merge-modal"
    >
      <div className="merge-modal-content">
        {/* Video Selection Section */}
        <div className="video-selection-section">
          <div className="section-header">
            <div className="header-left">
              <span className="section-title">选择要合成的视频</span>
              <span className="video-count">({availableVideos.length} 个可用)</span>
            </div>
            {availableVideos.length > 1 && (
              <div className="select-all">
                <Checkbox
                  checked={allSelected}
                  onChange={handleSelectAll}
                >
                  全选
                </Checkbox>
              </div>
            )}
          </div>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <div className="video-list">
              <SortableContext items={videos.map(video => video.requestId || `video-${videos.indexOf(video)}`)} strategy={verticalListSortingStrategy}>
                {videos.map((video, index) => {
                  const videoId = video.requestId || '';
                  const isSelected = selectedVideos.includes(videoId);

                  return (
                    <SortableVideoItem
                      key={videoId || index}
                      video={video}
                      index={index}
                      isSelected={isSelected}
                      onVideoSelect={handleVideoSelect}
                      selectedVideos={selectedVideos}
                    />
                  );
                })}
              </SortableContext>
            </div>
          </DndContext>
        </div>

        {/* Watermark Section */}
        <div className="watermark-section">
          <div className="watermark-toggle">
            <span className="toggle-label">添加水印</span>
            <Switch
              checked={addWatermark}
              onChange={setAddWatermark}
            />
          </div>
          <div className="watermark-description">
            {addWatermark ? '将在视频右下角添加水印' : '不添加水印'}
          </div>
        </div>

        {/* Progress Bar */}
        {(isMerging || isAddingWatermark) && (
          <div className="merge-progress">
            <div className="progress-bar-container">
              <div
                className="progress-bar-fill"
                style={{ width: `${isAddingWatermark ? watermarkProgress : mergeProgress}%` }}
              />
            </div>
            <div className="progress-text">
              {isMerging && '正在合成视频，请稍候...'}
              {isAddingWatermark && '正在添加水印，请稍候...'}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="action-buttons">
          <Button onClick={handleClose} disabled={isMerging || isAddingWatermark}>
            取消
          </Button>
          {(videoInfoList.mergedVideoKey || videoInfoList.markedVideoKey) && !isMerging && !isAddingWatermark && (
            <Button onClick={handleDownload} disabled={isMerging || isAddingWatermark}>
              下载
            </Button>
          )}
          <Button
            type="primary"
            onClick={handleMergeClick}
            loading={isMerging || isAddingWatermark}
            disabled={selectedCount === 0 || isMerging || isAddingWatermark}
            inline={true}
          >
            {isMerging ? `合成中... ${mergeProgress}%` :
             isAddingWatermark ? `添加水印中... ${watermarkProgress}%` :
             '开始合成'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

// Sortable Video Item Component
interface SortableVideoItemProps {
  video: VideoInfo;
  index: number;
  isSelected: boolean;
  onVideoSelect: (videoId: string, checked: boolean) => void;
  selectedVideos: string[];
}

const SortableVideoItem: React.FC<SortableVideoItemProps> = ({
  video,
  index,
  isSelected,
  onVideoSelect,
  selectedVideos,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: video.requestId || `video-${index}`,
  });

  // 优化视频封面图片URL，将短边限制为400px
  const optimizedImageUrl = useMemo(() => {
    return optimizeVideoPosterUrl(video.imageUrl, 400);
  }, [video.imageUrl]);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleItemClick = () => {
    const videoId = video.requestId || '';
    onVideoSelect(videoId, !isSelected);
  };

  const handleCheckboxChange = (checked: boolean) => {
    const videoId = video.requestId || '';
    onVideoSelect(videoId, checked);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`video-item ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''}`}
      onClick={handleItemClick}
    >
      <Checkbox
        checked={isSelected}
        onChange={handleCheckboxChange}
        className="video-checkbox"
      />

      <div className="video-preview">
        <img
          src={optimizedImageUrl}
          alt={`视频 ${index + 1}`}
          className="video-thumbnail"
        />
        <div className="video-overlay">
          <span className="video-duration">
            {Math.round(video.duration)}s
          </span>
        </div>
      </div>
      <div className="video-info">
        <div className="video-prompt">
          {video.positivePrompt.length > 30
            ? `${video.positivePrompt.substring(0, 30)}...`
            : video.positivePrompt
          }
        </div>
        <div className="video-quality">{video.quality}</div>
      </div>
      <div
        className="drag-handle"
        {...attributes}
        {...listeners}
        onClick={(e) => e.stopPropagation()}
      >
        <DragOutlined />
      </div>
    </div>
  );
};

export default VideoMergeModal;
