.video-merge-modal {
  &.dtm-modal-transparent {
    width: 520px;
  }

  :global {
    .adm-modal-content {
      border-radius: 8px;
      overflow: hidden;
      background: #1a1a1a;
    }

    .adm-modal-header {
      border-bottom: 1px solid #333333;
      padding: 16px 24px;
      margin: 0;

      .adm-modal-title {
        font-size: 18px;
        line-height: 1.4;
        color: #ffffff;
        font-weight: 600;
      }
    }

    .adm-modal-body {
      padding: 0;
      margin: 0;
    }

    .adm-modal-close {
      display: none; // 隐藏默认的关闭按钮
    }

    .adm-modal-footer {
      padding: 0;
      margin: 0;
    }
  }

  .modal-title-with-close {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    span {
      font-size: 18px;
      line-height: 1.4;
      color: #ffffff;
      font-weight: 600;
    }

    .close-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border: none;
      background: transparent;
      color: #999999;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.2s ease-out;

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.1);
        color: #cccccc;
      }

      &:active:not(:disabled) {
        background: rgba(255, 255, 255, 0.2);
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }

      .anticon {
        font-size: 16px;
      }
    }
  }
}

.merge-modal-content {
  .video-selection-section {
    padding: 20px 24px;
    border-bottom: 1px solid #333333;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;

        .section-title {
          font-size: 17px;
          line-height: 1.5;
          font-weight: bold;
          color: #ffffff;
        }

        .video-count {
          font-size: 12px;
          line-height: 1.4;
          color: #999999;
        }
      }

      .select-all {
        user-select: none; // 防止文字被选中

        :global {
          .adm-checkbox {
            font-size: 12px;
            line-height: 1.4;
            color: #cccccc;
            cursor: pointer;
          }
        }
      }
    }

    .video-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      max-height: 300px;
      overflow-y: auto;

              .video-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 8px;
                border: 1px solid #333333;
                border-radius: 6px;
                background: #2a2a2a;
                transition: all 0.2s ease-out;
                cursor: pointer;

                &:hover {
                  border-color: #007fff;
                  background: #333333;
                }

                &.selected {
                  border-color: #007fff;
                  background: #333333;
                  box-shadow: 0 0 0 2px rgba(0, 127, 255, 0.2);
                }

                &.dragging {
                  opacity: 0.5;
                  transform: rotate(5deg);
                  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
                  z-index: 1000;
                }

        .video-checkbox {
          flex-shrink: 0;
        }

        .drag-handle {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          margin-left: 8px;
          color: #666666;
          cursor: grab;
          border-radius: 4px;
          transition: all 0.2s ease-out;
          user-select: none;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #cccccc;
          }

          &:active {
            cursor: grabbing;
            transform: scale(0.95);
          }

          .anticon {
            font-size: 14px;
            pointer-events: none;
          }
        }

        // Mobile drag handle adjustments
        @media (max-width: 768px) {
          .drag-handle {
            width: 28px;
            height: 28px;
            margin-left: 12px;

            .anticon {
              font-size: 16px;
            }
          }
        }

        .video-preview {
          position: relative;
          flex-shrink: 0;

          .video-thumbnail {
            width: 80px;
            height: 45px;
            object-fit: cover;
            border-radius: 4px;
          }

          .video-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.6);
            padding: 2px 4px;
            border-radius: 4px 0 0 0;

            .video-duration {
              font-size: 10px;
              line-height: 1;
              color: #ffffff;
              line-height: 1;
            }
          }
        }

        .video-info {
          flex: 1;
          min-width: 0;
          text-align: left;

          .video-prompt {
            font-size: 12px;
            line-height: 1.4;
            color: #ffffff;
            line-height: 1.4;
            margin-bottom: 4px;
            word-break: break-word;
          }

          .video-quality {
            font-size: 11px;
            line-height: 1;
            color: #999999;
          }
        }
      }
    }
  }

  .watermark-section {
    padding: 20px 24px;

    .watermark-toggle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .toggle-label {
        font-size: 17px;
        line-height: 1.5;
        color: #ffffff;
        font-weight: 500;
      }
    }

    .watermark-description {
      font-size: 12px;
      line-height: 1.4;
      color: #999999;
      line-height: 1.4;
      text-align: right;
    }
  }

  .action-buttons {
    padding: 20px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #333333;

    :global {
      .adm-button {
        min-width: 120px; // 增加最小宽度以容纳较长的文本，避免闪动
        height: 32px;
        border-radius: 4px;

        &.adm-button-fill {
          background: #007fff;
          border-color: #007fff;

          &:hover {
            background: #0056cc;
            border-color: #0056cc;
          }

          &:disabled {
            background: #333333;
            border-color: #333333;
            color: #666666;
          }
        }
      }
    }
  }
}

// Mobile styles
@media (max-width: 768px) {
  .video-merge-modal {
    &.dtm-modal-transparent {
      width: 80%;
    }

    :global {
      .adm-modal-content {
        margin: 20px;
        border-radius: 8px;
      }

      .adm-modal-header {
        padding: 16px 20px;

        .adm-modal-title {
          font-size: 18px;
          line-height: 1.4;
        }
      }
    }
  }

  .merge-modal-content {
    .video-selection-section,
    .watermark-section,
    .action-buttons {
      padding-left: 20px;
      padding-right: 20px;
    }

    .video-selection-section .video-list {
      max-height: 250px;

      .video-item {
        padding: 10px;

        .video-preview .video-thumbnail {
          width: 70px;
          height: 40px;
        }

        .video-info .video-prompt {
          font-size: 17px;
          line-height: 1.5;
        }
      }
    }

    .action-buttons {
      flex-direction: column-reverse;

      :global {
        .adm-button {
          width: 100%;
          height: 40px;
          font-size: 17px;
          line-height: 1.5;

          &:first-child {
            margin-top: 8px;
          }
        }
      }
    }
  }
}

// Scrollbar styles
.video-list {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #333333;
    border-radius: 4px;
    transition: all 0.2s ease-out;

    &:hover {
      background: #555555;
    }
  }
}

// Progress bar styles
.merge-progress {
  margin-top: 16px;
  text-align: center;

  .progress-bar-container {
    width: 100%;
    height: 8px;
    background-color: #333333;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
    position: relative;
  }

  .progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #007fff, #00ff00);
    border-radius: 8px;
    transition: width 0.3s ease;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%
      );
      animation: progress-shine 2s infinite;
    }
  }

  .progress-text {
    color: #ffffff;
    font-size: 12px;
  }
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
