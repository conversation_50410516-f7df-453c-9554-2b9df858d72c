import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, To<PERSON>, Drawer, ConfigProvider } from 'dingtalk-design-mobile';
import { Popover } from 'dingtalk-design-desktop';
import {
  InformationThereOutlined,
  CopyOutlined,
  CheckCircleFilled,
  CloseCircledFilled,
} from '@ali/ding-icons';
import { isMobileDevice, setPageTitle } from '@/utils/jsapi';
import { sendUT } from '@/utils/trace';
import ImageUploader from '@/components/ImageUploader';
import { QuotaUsageResponse } from '@/apis/quota';
import QuotaDisplay from '@/components/QuotaDisplay';
import './index.less';

interface HelpContentItem {
  type: 'text' | 'image' | 'section' | 'examples';
  text?: string;
  src?: string;
  alt?: string;
  title?: string;
  items?: string[];
  status?: 'correct' | 'incorrect';
  isTwoImage?: boolean;
  images?: Array<{
    src: string;
    alt?: string;
    label: string;
  }>;
}

interface HelpContent {
  title: string;
  content: HelpContentItem[];
}

interface HelpContents {
  [key: string]: HelpContent;
}

interface VideoFormProps {
  state: {
    uploadedImage: string | string[] | null;
    positivePrompt: string | string[];
    negativePrompt: string;
    quality: string;
    isGenerating: boolean;
    error: string | null;
  };
  onUpdate: (
    updates: Partial<{
      uploadedImage: string | string[] | null;
      positivePrompt: string | string[];
      negativePrompt: string;
      quality: string;
      isGenerating: boolean;
      error: string | null;
    }>,
  ) => void;
  onGenerate: () => void;
  quotaUsage?: QuotaUsageResponse | null;
  multiple?: boolean; // 新增 multiple 属性
}

const VideoForm: React.FC<VideoFormProps> = ({ state, onUpdate, onGenerate, quotaUsage, multiple = false }) => {
  const [qualityOption, setQualityOption] = useState(state.quality || 'high');
  const [helpDrawerVisible, setHelpDrawerVisible] = useState(false);
  const [currentHelpContent, setCurrentHelpContent] = useState<HelpContent | null>(null);
  const [currentHelpKey, setCurrentHelpKey] = useState<string>('');
  const isMobile = isMobileDevice();

  // 获取当前图片数组（兼容单张和多张模式）
  const currentImages = multiple
    ? (Array.isArray(state.uploadedImage) ? state.uploadedImage : (state.uploadedImage ? [state.uploadedImage] : []))
    : [];

  // 获取当前单张图片（单张模式）
  const currentSingleImage = multiple ? null : (typeof state.uploadedImage === 'string' ? state.uploadedImage : null);

  // 获取当前提示词数组（兼容单张和多张模式）
  const currentPrompts = multiple
    ? (Array.isArray(state.positivePrompt) ? state.positivePrompt : (state.positivePrompt ? [state.positivePrompt] : []))
    : [];

  // 获取当前单张提示词（单张模式）
  const currentSinglePrompt = multiple ? '' : (typeof state.positivePrompt === 'string' ? state.positivePrompt : '');

  useEffect(() => {
    setPageTitle(i18next.t('j-dingtalk-web_pages_aiVideo_AiVideoGeneration'));
  }, []);

  // Sync local quality state with parent state
  useEffect(() => {
    setQualityOption(state.quality || 'high');
  }, [state.quality]);

  // Help content data
  const helpContents: HelpContents = {
    imageUpload: {
      title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ImageSpecification'),
      content: [
        {
          type: 'section',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_MaterialType'),
          items: [
            i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_TheElementsAreSimpleThe'),
            i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ItIsRecommendedToUpload'),
          ],
        },
        {
          type: 'section',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_Size'),
          items: [
            i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_SupportsJpgPngFormatWith'),
            i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_TheShortSideIsNot'),
          ],
        },
        {
          type: 'examples',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_CorrectExample'),
          status: 'correct',
          isTwoImage: true,
          images: [
            {
              src: 'https://img.alicdn.com/imgextra/i1/O1CN01TiAyLW24kXS8EGPRy_!!6000000007429-2-tps-534-510.png',
              label: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_HandheldProductWithClearText',
              ),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN0187V8vc1mfoHQTOAii_!!6000000004982-2-tps-534-510.png',
              label: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_TheProductIsClearAnd',
              ),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN01szA2Su1Kq3u76Xqku_!!6000000001214-2-tps-534-510.png',
              label: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelHalfLengthDisplayClothing',
              ),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i2/O1CN015406fZ1ZjONAnRfML_!!6000000003230-2-tps-534-510.png',
              label: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelTrialProducts',
              ),
            },
          ],
        },
        {
          type: 'examples',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ErrorExample'),
          status: 'incorrect',
          isTwoImage: true,
          images: [
            {
              src: 'https://img.alicdn.com/imgextra/i3/O1CN01OmVXMg1CXj7dSQDNL_!!6000000000091-2-tps-534-510.png',
              label: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_StitchingPicture',
              ),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i1/O1CN01k8SmyE1L7vLhAbYea_!!6000000001253-2-tps-534-510.png',
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_UnclearSubject'),
            },
          ],
        },
      ],
    },
    positivePrompt: {
      title: i18next.t(
        'j-dingtalk-web_pages_aiVideo_components_VideoForm_CreativeDescriptionReference',
      ),
      content: [
        {
          type: 'section',
          title: i18next.t(
            'j-dingtalk-web_pages_aiVideo_components_VideoForm_DescribeThePictureAndAction',
          ),
          items: [],
        },
        {
          type: 'examples',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ReferenceExample'),
          status: 'correct',
          images: [
            {
              src: 'https://img.alicdn.com/imgextra/i2/O1CN01CObnsc1fBOfXHR5S7_!!6000000003968-1-tps-480-316.gif',
              alt: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_MenSlowlyPickUpThe',
              ),
              label: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptMenSlowlyPickUp',
              ),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN01Jk5kuk1voMDFxu3Wy_!!6000000006219-1-tps-320-320.gif',
              alt: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_TheLensSlowlyRotatesTo',
              ),
              label: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptTheLensSlowlyRotates',
              ),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN01f8ASEy1eQgGjAGtxN_!!6000000003866-1-tps-320-320.gif',
              alt: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelPosingShowingYogaClothes',
              ),
              label: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptModelPosingShowingYoga',
              ),
            },
          ],
        },
      ],
    },
    negativePrompt: {
      title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ContentYouDonTWant'),
      content: [],
    },
  };

  const handleImageUpload = (imageUrl: string | string[] | null) => {
    sendUT('aigc_video_image_upload', {
      device: isMobile ? 'mobile' : 'pc',
      imageUrl,
    });

    // 当图片发生变化时，同步调整 positivePrompt 数组
    if (multiple && Array.isArray(imageUrl)) {
      const newPrompts = [...currentPrompts];
      const targetLength = Math.max(1, imageUrl.length); // 至少保持 1 个提示词

      // 如果图片数量增加，为新图片添加空的提示词
      while (newPrompts.length < targetLength) {
        newPrompts.push('');
      }
      // 如果图片数量减少，移除多余的提示词，但至少保留 1 个
      if (newPrompts.length > targetLength) {
        newPrompts.splice(targetLength);
      }
      onUpdate({
        uploadedImage: imageUrl,
        positivePrompt: newPrompts
      });
    } else {
      onUpdate({ uploadedImage: imageUrl });
    }
  };

  const handlePromptChange = (field: string, value: string, index?: number) => {
    if (multiple && field === 'positivePrompt' && typeof index === 'number') {
      // 多图模式下的提示词修改
      const newPrompts = [...currentPrompts];
      newPrompts[index] = value;
      onUpdate({ positivePrompt: newPrompts });
    } else {
      // 单图模式下的修改
      onUpdate({ [field]: value });
    }
  };

  const handleCopyPrompt = (promptText: string) => {
    // Update positive prompt field
    onUpdate({ positivePrompt: promptText });

    // Show success message
    Toast.success({
      content: i18next.t(
        'j-dingtalk-web_pages_aiVideo_components_VideoForm_CopiedToCreativeDescription',
      ),
      duration: 2,
      position: 'top',
      maskClickable: true,
    });

    setHelpDrawerVisible(false);
  };

  // 验证表单是否可以提交
  const isFormValid = () => {
    if (multiple) {
      // 多图模式：至少需要第一个提示词不为空
      const firstPrompt = currentPrompts[0] || '';
      const hasValidFirstPrompt = firstPrompt.trim().length > 0;

      // 如果有多张图片，每张图片都需要对应的提示词
      if (currentImages.length > 1) {
        return currentImages.every((_, index) => {
          const prompt = currentPrompts[index] || '';
          return prompt.trim().length > 0;
        });
      }

      // 0 或 1 张图片时，只需要第一个提示词不为空
      return hasValidFirstPrompt;
    } else {
      // 单图模式：需要有图片和提示词
      return !!currentSingleImage && currentSinglePrompt.trim().length > 0;
    }
  };

  const handleHelpClick = (helpKey: string) => {
    sendUT('aigc_video_guide', {
      device: isMobile ? 'mobile' : 'pc',
      helpKey,
    });

    const helpContent = helpContents[helpKey];
    if (helpContent) {
      setCurrentHelpContent(helpContent);
      setCurrentHelpKey(helpKey);
      if (isMobile) {
        setHelpDrawerVisible(true);
      }
    }
  };

  const handleSubmit = () => {
    onGenerate();
  };

  const qualityOptions = [
    { label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_Hd'), value: 'high' },
    {
      label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_StandardClear'),
      value: 'standard',
    },
  ];

  // Form title component with help icon
  const FormTitle: React.FC<{ title: string; helpKey: string; onlyTitle?: boolean }> = ({
    title,
    helpKey,
    onlyTitle,
  }) => {
    const helpContent = helpContents[helpKey];

    const InfoIcon = () => {
      return (
        <button className="example-btn" onClick={() => handleHelpClick(helpKey)}>
          <InformationThereOutlined />
          {i18next.t('j-dingtalk-web_pages_ai-video_components_VideoForm_SpecificationReference')}
        </button>
      );
    };

    if (isMobile) {
      return (
        <div className="form-title">
          <span className="form-title-text">{title}</span>
          {!onlyTitle && <InfoIcon />}
        </div>
      );
    } else {
      // PC端：使用 Popover 组件，鼠标悬停显示在右边
      const renderHelpContent = () => (
        <div className="help-popover-content">
          {helpContent?.content.map((item: HelpContentItem, index: number) => {
            const renderContent = () => {
              if (item.type === 'text') {
                return <p className="help-text">{item.text}</p>;
              }

              if (item.type === 'image') {
                return <img src={item.src} alt={item.alt} className="help-content-image" />;
              }

              if (item.type === 'section') {
                return (
                  <div className="help-section">
                    {item.items && item.items.length > 0 ? (
                      <>
                        <h4 className="help-section-title">{item.title}</h4>
                        <ul className="help-section-list">
                          {item.items.map((listItem, listIndex) => (
                            <li key={`section-item-${listIndex}`} className="help-section-item">
                              {listItem}
                            </li>
                          ))}
                        </ul>
                      </>
                    ) : (
                      <p className="help-section-description">{item.title}</p>
                    )}
                  </div>
                );
              }

              if (item.type === 'examples') {
                return (
                  <div
                    className={`help-examples ${
                      item.status === 'correct' ? 'correct' : 'incorrect'
                    }`}
                  >
                    <div className="help-examples-header">
                      <span
                        className={`help-examples-icon ${
                          item.status === 'correct' ? 'correct' : 'incorrect'
                        }`}
                      >
                        {item.status === 'correct' ? <CheckCircleFilled /> : <CloseCircledFilled />}
                      </span>
                      <h4
                        className={`help-examples-title ${
                          item.status === 'correct' ? 'correct' : 'incorrect'
                        }`}
                      >
                        {item.title}
                      </h4>
                    </div>
                    <div
                      className={`help-examples-grid ${
                        helpKey === 'positivePrompt' ? 'single-column' : ''
                      } ${item.isTwoImage ? 'force-two-columns' : ''}`}
                    >
                      {item.images?.map((image, imageIndex) => (
                        <div key={`example-image-${imageIndex}`} className="help-example-item">
                          {image.alt && (
                            <div className="help-example-copy-container">
                              <button
                                className="help-example-copy-btn"
                                onClick={() => handleCopyPrompt(image.alt!)}
                                title={i18next.t(
                                  'j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription',
                                )}
                              >
                                <CopyOutlined className="copy-icon" />
                              </button>
                            </div>
                          )}
                          <div className="help-example-image-placeholder">
                            <img
                              src={image.src}
                              alt={image.alt}
                              className="help-example-image"
                              loading="lazy"
                            />

                            <div className="help-example-label-overlay">
                              <p className="help-example-label">{image.label}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              }

              return null;
            };

            return (
              <div key={`help-content-${index}`} className="help-content-item">
                {renderContent()}
              </div>
            );
          })}
        </div>
      );

      return (
        <div className="form-title">
          <span className="form-title-text">{title}</span>
          {!onlyTitle && (
            <Popover
              title={helpContent?.title}
              content={renderHelpContent()}
              placement="right"
              trigger="hover"
              arrow={false}
              overlayClassName="form-help-popover"
            >
              <button className="example-btn">
                <InformationThereOutlined />
                {i18next.t('j-dingtalk-web_pages_ai-video_components_VideoForm_SpecificationReference')}
              </button>
            </Popover>
          )}
        </div>
      );
    }
  };

  return (
    <div className="video-form">
      <div className="form-container">
        {/* Image Upload Section */}
        <div className="form-section">
          <FormTitle
            title={i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_VideoForm_FirstFrameVideoPicture',
            )}
            helpKey="imageUpload"
          />
          <ImageUploader
            multiple={multiple}
            value={state.uploadedImage}
            onChange={handleImageUpload}
            sizeConstraints={{ minWidth: 300, minHeight: 300, maxWidth: 4000, maxHeight: 4000 }}
          />
        </div>

        {/* Positive Prompt Section */}
        <div className="form-section">
          <FormTitle
            title={i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_VideoForm_YourCreativeDescription',
            )}
            helpKey="positivePrompt"
          />
          {multiple ? (
            // 多图模式：显示多个 textarea，当图片为 0 或 1 时默认显示 1 个
            <div className="multiple-textarea-container">
              {Array.from({ length: Math.max(1, currentImages.length) }).map((_, index) => (
                <div key={index} className="textarea-container">
                  <div className="textarea-label">
                    {i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_Image')} {index + 1}:
                  </div>
                  <textarea
                    className="custom-textarea"
                    placeholder={i18next.t(
                      'j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsModelDisplay',
                    )}
                    value={currentPrompts[index] || ''}
                    onChange={(e) => handlePromptChange('positivePrompt', e.target.value, index)}
                    maxLength={200}
                  />
                  <div className="char-count">{(currentPrompts[index] || '').length} / 200</div>
                </div>
              ))}
            </div>
          ) : (
            // 单图模式：保持原有逻辑
            <div className="textarea-container">
              <textarea
                className="custom-textarea"
                placeholder={i18next.t(
                  'j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsModelDisplay',
                )}
                value={currentSinglePrompt}
                onChange={(e) => handlePromptChange('positivePrompt', e.target.value)}
                maxLength={200}
              />
              <div className="char-count">{currentSinglePrompt.length} / 200</div>
            </div>
          )}
        </div>

        {/* Negative Prompt Section */}
        <div className="form-section">
          <FormTitle
            title={i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_VideoForm_ContentYouDonTWant',
            )}
            helpKey="negativePrompt"
            onlyTitle
          />
          <div className="textarea-container">
            <textarea
              className="custom-textarea negative-textarea"
              placeholder={i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsDistortionDistortion',
              )}
              value={state.negativePrompt}
              onChange={(e) => handlePromptChange('negativePrompt', e.target.value)}
              maxLength={200}
            />

            <div className="char-count">{state.negativePrompt.length} / 200</div>
          </div>
        </div>

        {/* Video Quality Section */}
        <div className="form-section">
          <div className="quality-section">
            <div className="quality-label">
              {i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDefinition')}
            </div>
            <div className="quality-toggle-container">
              <div className="quality-toggle">
                <div
                  className={`quality-slider ${
                    qualityOption === 'standard' ? 'slide-right' : 'slide-left'
                  }`}
                />

                {qualityOptions.map((option) => (
                  <button
                    key={option.value}
                    className={`quality-option ${qualityOption === option.value ? 'active' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent event bubbling
                      setQualityOption(option.value);
                      onUpdate({ quality: option.value });
                    }}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Video Duration Section */}
        <div className="form-section">
          <div className="duration-section">
            <div className="duration-label">
              {i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDuration')}
            </div>
            <div className="duration-value">5s</div>
          </div>
        </div>

        {/* Error Display */}
        {state.error && <div className="error-message">{state.error}</div>}

        {/* Quota Information */}
        {quotaUsage && (
          <div className="quota-info">
            <div className="quota-item">
              {i18next.t('j-dingtalk-web_pages_ai-video_components_VideoForm_ThisTimeWillConsume')}
            </div>
            <div className="quota-divider" />
            <div className={`quota-item ${quotaUsage && ((quotaUsage.totalQuota || 0) - (quotaUsage.usedQuota || 0)) === 0 ? 'quota-zero' : ''}`}>
              {i18next.t('j-dingtalk-web_pages_ai-video_components_VideoForm_UsageCount')}
              <QuotaDisplay quotaUsage={quotaUsage} />
            </div>
          </div>
        )}

        {/* Generate Button */}
        <div className="form-actions">
          <Button
            type="primary"
            size="large"
            className="generate-btn"
            loading={state.isGenerating}
            onClick={handleSubmit}
            disabled={!isFormValid() || state.isGenerating}
          >
            {state.isGenerating
              ? i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_Generating')
              : i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_GenerateNow')}
          </Button>
        </div>
      </div>

      {/* Help Drawer for Mobile */}
      {isMobile && (
        <ConfigProvider config={{ adapt: false }}>
          <Drawer
            size="large"
            title={
              currentHelpContent?.title ||
              i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_HelpInformation')
            }
            visible={helpDrawerVisible}
            onVisibleChange={setHelpDrawerVisible}
          >
            <div className="help-drawer-content">
              {currentHelpContent?.content.map((item: HelpContentItem, index: number) => {
                const renderDrawerContent = () => {
                  if (item.type === 'text') {
                    return <p className="help-text">{item.text}</p>;
                  }

                  if (item.type === 'image') {
                    return <img src={item.src} alt={item.alt} className="help-content-image" />;
                  }

                  if (item.type === 'section') {
                    return (
                      <div className="help-section">
                        {item.items && item.items.length > 0 ? (
                          <>
                            <h4 className="help-section-title">{item.title}</h4>
                            <ul className="help-section-list">
                              {item.items.map((listItem, listIndex) => (
                                <li
                                  key={`drawer-section-item-${listIndex}`}
                                  className="help-section-item"
                                >
                                  {listItem}
                                </li>
                              ))}
                            </ul>
                          </>
                        ) : (
                          <p className="help-section-description">{item.title}</p>
                        )}
                      </div>
                    );
                  }

                  if (item.type === 'examples') {
                    return (
                      <div
                        className={`help-examples ${
                          item.status === 'correct' ? 'correct' : 'incorrect'
                        }`}
                      >
                        <div className="help-examples-header">
                          <span
                            className={`help-examples-icon ${
                              item.status === 'correct' ? 'correct' : 'incorrect'
                            }`}
                          >
                            {item.status === 'correct' ? (
                              <CheckCircleFilled />
                            ) : (
                              <CloseCircledFilled />
                            )}
                          </span>
                          <h4
                            className={`help-examples-title ${
                              item.status === 'correct' ? 'correct' : 'incorrect'
                            }`}
                          >
                            {item.title}
                          </h4>
                        </div>
                        <div
                          className={`help-examples-grid ${
                            currentHelpKey === 'positivePrompt' ? 'single-column' : ''
                          } ${item.isTwoImage ? 'force-two-columns' : ''}`}
                        >
                          {item.images?.map((image, imageIndex) => (
                            <div
                              key={`drawer-example-image-${imageIndex}`}
                              className="help-example-item"
                            >
                              {image.alt && (
                                <div className="help-example-copy-container">
                                  <button
                                    className="help-example-copy-btn"
                                    onClick={() => handleCopyPrompt(image.alt!)}
                                    title={i18next.t(
                                      'j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription',
                                    )}
                                  >
                                    <CopyOutlined className="copy-icon" />
                                  </button>
                                </div>
                              )}
                              <div className="help-example-image-placeholder">
                                <img
                                  src={image.src}
                                  alt={image.alt}
                                  className="help-example-image"
                                  loading="lazy"
                                />
                                <div className="help-example-label-overlay">
                                  <p className="help-example-label">{image.label}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  }

                  return null;
                };

                return (
                  <div key={`drawer-help-content-${index}`} className="help-content-item">
                    {renderDrawerContent()}
                  </div>
                );
              })}
            </div>
          </Drawer>
        </ConfigProvider>
      )}
    </div>
  );
};

export default VideoForm;
