import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import {
  PlayOutlined,
  PauseFilled,
  // PlayerFullscreenLOutlined,
  DownArrowOutlined,
  MoreOutlined,
} from '@ali/ding-icons';
// import { isMobileDevice } from '@/utils/jsapi';
import { optimizeVideoPosterUrl } from '@/utils/imageOptimization';
import './index.less';

// Extend HTMLVideoElement interface to include webkit methods
// interface ExtendedHTMLVideoElement extends HTMLVideoElement {
//   webkitRequestFullscreen?: () => void;
//   mozRequestFullScreen?: () => void;
//   msRequestFullscreen?: () => void;
// }

interface VideoInfo {
  videoUuid?: string;
  videoUrl: string;
  imageUrl: string;
  gifUrl?: string;
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  duration: number;
  userRating?: string;
  requestId?: string;
  createdAt?: string;
  status?: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoItemProps {
  videoInfo: VideoInfo;
  showPromptHeader?: boolean; // Whether to show the prompt header
  className?: string;
  progress?: number; // Video generation progress (0-100)
  onVideoMenuClick?: (videoInfo: VideoInfo) => void; // 点击视频右上角菜单
}

const VideoItem: React.FC<VideoItemProps> = ({
  videoInfo,
  showPromptHeader = false, // 默认不显示prompt header
  className = '',
  progress = 0,
  onVideoMenuClick,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 优化封面图片URL，将短边限制为400px
  const optimizedPosterUrl = useMemo(() => {
    return optimizeVideoPosterUrl(videoInfo.imageUrl, 400);
  }, [videoInfo.imageUrl]);

  // Check if current device is mobile
  // const isMobile = isMobileDevice();

  // Linear progress component for video generation
  const LinearProgress: React.FC<{progress: number}> = ({ progress: progressValue }) => {
    return (
      <div className="linear-progress-container">
        <div className="progress-title">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Generating')}&nbsp;{Math.round(progressValue)}%</div>
        <div className="linear-progress">
          <div className="progress-track">
            <div
              className="progress-fill"
              style={{ width: `${progressValue}%` }}
            />
          </div>
        </div>
        <div className="progress-tip">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ItIsExpectedToBe')}</div>
      </div>);
  };

  // Format time for video player
  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Convert quality value to Chinese text
  const getQualityText = (quality: string): string => {
    switch (quality?.toLowerCase()) {
      case 'hd':
      case 'high':
      case i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Hd'):
        return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Hd');
      case 'sd':
      case 'standard':
      case i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_StandardClear'):
        return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_StandardClear');
      default:
        return quality;
    }
  };

  // Handle video play/pause - memoized
  const handlePlayPause = useCallback(() => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.controls = false;
        videoRef.current.play();
      }
    }
  }, [isPlaying]);

  // Handle fullscreen - memoized
  // const handleFullscreen = useCallback(async () => {
  //   if (!videoRef.current) return;

  //   const video = videoRef.current as ExtendedHTMLVideoElement;

  //   // Check if running in iframe environment
  //   const isInIframe = window.self !== window.top;

  //   // In iframe environment, fullscreen is usually blocked
  //   if (isInIframe) {
  //     return;
  //   }

  //   try {
  //     if (isMobile) {
  //       if (video.requestFullscreen) {
  //         video.requestFullscreen();
  //       } else {
  //         video.controls = false;
  //         video.play();
  //       }
  //     } else if (video.requestFullscreen) {
  //       video.requestFullscreen();
  //     } else if (video.webkitRequestFullscreen) {
  //       video.webkitRequestFullscreen();
  //     } else if (video.mozRequestFullScreen) {
  //       video.mozRequestFullScreen();
  //     } else if (video.msRequestFullscreen) {
  //       video.msRequestFullscreen();
  //     }
  //   } catch (error) {
  //     console.warn('Fullscreen request failed:', error);
  //   }
  // }, [isMobile]);

  // Handle video time update - memoized
  const handleTimeUpdate = useCallback(() => {
    if (videoRef.current && !isNaN(videoRef.current.currentTime)) {
      setCurrentTime(videoRef.current.currentTime);
    }
  }, []);

  // Handle video metadata loaded - memoized
  const handleLoadedMetadata = useCallback(() => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setCurrentTime(0);
    }
  }, []);

  // Handle prompt toggle - memoized
  const handlePromptToggle = useCallback(() => {
    setIsPromptExpanded((prev) => !prev);
  }, []);

  // Handle menu click
  const handleMenuClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onVideoMenuClick) {
      onVideoMenuClick(videoInfo);
    }
  }, [onVideoMenuClick, videoInfo]);

  // Reset video states when videoUrl changes
  useEffect(() => {
    if (videoInfo.videoUrl) {
      setCurrentTime(0);
      setDuration(0);
      setIsPlaying(false);
    }
  }, [videoInfo.videoUrl]);

  // Add event listeners for video
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      video.addEventListener('timeupdate', handleTimeUpdate);
      video.addEventListener('loadedmetadata', handleLoadedMetadata);

      return () => {
        video.removeEventListener('timeupdate', handleTimeUpdate);
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      };
    }
  }, [videoInfo.videoUrl, handleTimeUpdate, handleLoadedMetadata]);

  return (
    <div className={`video-item ${className}`} ref={containerRef}>
      {/* Prompt Header - optional */}
      {showPromptHeader && (
        <div className="video-item-header">
          <div className="prompt-info-container">
            <div className={`prompt-info ${isPromptExpanded ? 'expanded' : 'collapsed'}`}>
              {getQualityText(videoInfo.quality)} / {videoInfo.duration}s /
              {videoInfo.positivePrompt}
            </div>
            <div
              className={`prompt-toggle-icon ${isPromptExpanded ? 'expanded' : ''}`}
              onClick={handlePromptToggle}
            >
              <DownArrowOutlined />
            </div>
          </div>
        </div>
      )}

      {/* Video Content */}
      <div className="video-content">
        {videoInfo.status === 'finish' && videoInfo.videoUrl ? (
          <div className="video-container">
            {/* 右上角菜单按钮 */}
            <div className="video-menu-button" onClick={handleMenuClick}>
              <MoreOutlined />
            </div>
            <video
              ref={videoRef}
              src={videoInfo.videoUrl}
              className="video-player"
              poster={optimizedPosterUrl}
              preload="none"
              controls={false}
              x5-video-player-fullscreen="true"
              x5-video-player-type="h5"
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onEnded={() => setIsPlaying(false)}
            />
            <div className="video-controls">
              <div className="control-left">
                <button className="play-button" onClick={handlePlayPause}>
                  {isPlaying ? <PlayOutlined /> : <PauseFilled />}
                </button>
              </div>
              <div className="control-right">
                <span className="time-display">
                  {formatTime(currentTime)}/{formatTime(duration || +videoInfo.duration)}
                </span>
                {/* <button className="fullscreen-button" onClick={handleFullscreen}>
                  <PlayerFullscreenLOutlined />
                </button> */}
              </div>
            </div>
          </div>
        ) : (
          <div className="video-placeholder">
            {videoInfo.status === 'failed' && (
              <div className="video-menu-button" onClick={handleMenuClick}>
                <MoreOutlined />
              </div>
            )}
            <img src={optimizedPosterUrl} alt="Video thumbnail" className="thumbnail-image" />
            <div className="status-overlay">
              {videoInfo.status === 'pending' && (
                <div className="status-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_WaitingForGeneration')}</div>
              )}
              {videoInfo.status === 'processing' && (
                <div className="status-processing">
                  <LinearProgress progress={progress} />
                </div>
              )}
              {videoInfo.status === 'failed' && (
                <div className="status-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToGenerate')}</div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Custom comparison function for React.memo
const areEqual = (prevProps: VideoItemProps, nextProps: VideoItemProps) => {
  return (
    prevProps.videoInfo.videoUuid === nextProps.videoInfo.videoUuid &&
    prevProps.videoInfo.status === nextProps.videoInfo.status &&
    prevProps.videoInfo.videoUrl === nextProps.videoInfo.videoUrl &&
    prevProps.videoInfo.gifUrl === nextProps.videoInfo.gifUrl &&
    prevProps.videoInfo.userRating === nextProps.videoInfo.userRating &&
    prevProps.progress === nextProps.progress &&
    prevProps.className === nextProps.className &&
    prevProps.showPromptHeader === nextProps.showPromptHeader
  );
};

// Export memoized component
export default React.memo(VideoItem, areEqual);