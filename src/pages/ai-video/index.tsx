import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useState, useRef, useMemo } from 'react';
import { Toast } from 'dingtalk-design-mobile';
import theme, { IThemeType } from 'dingtalk-theme';
import { isDingTalk, isMobileDevice, setPageTitle, openDualLink } from '@/utils/jsapi';
import { sendUT, initPageTimeTracking, PageTimeTracker } from '@/utils/trace';
import { generateMultiVideo, checkMultiVideoStatus, listMultiVideos } from '@/apis';
import type { GenerateMultiVideoRequest, ListVideoResponse, CheckMultiVideoStatusResponse } from '@/apis/video';
import { getOrderListUrl, getCreateOrderUrl } from '@/utils/env';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { EditOutlined, MoreOutlined } from '@ali/ding-icons';
import Loading from '@/components/Loading';
import MobileNavbar from '@/components/MobileNavbar';
import FreeModal from '@/components/FreeModal';
import useQuotaUsage from '@/hooks/useQuotaUsage';
import useFreeQuota from '@/hooks/useFreeQuota';
import { log } from '@/utils/console';
import WelcomeScreen from './components/WelcomeScreen';
import VideoForm from './components/VideoForm';
import VideoList from './components/VideoList';
import './index.less';

theme.setTheme(IThemeType.dark);

// Video information interface
interface VideoInfo {
  videoUuid?: string;
  videoUrl: string;
  imageUrl: string;
  gifUrl?: string;
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  duration: number;
  requestId?: string;
  createdAt?: string;
  status?: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoInfoList {
  createdAt: string;
  updatedAt: string;
  operator?: string;
  uuid: string;
  userRating?: string;
  mergedVideoKey?: string;
  markedVideoKey?: string;
  taskMode?: 'SINGLE' | 'MULTI';
  datas: VideoInfo[];
  status: 'pending' | 'processing' | 'finish' | 'failed';
}

interface AIVideoState {
  currentStep: 'welcome' | 'form' | 'videoList';
  rightPanelContent: 'welcome' | 'videoList';
  uploadedImage: string[] | string | null;
  positivePrompt: string[] | string;
  negativePrompt: string;
  multiple?: boolean;
  quality: string;
  isGenerating: boolean;
  error: string | null;
  taskId: string | null;
  progress: number;
  videoList: VideoInfoList[];
  isLoadingVideos: boolean;
  hasVideos: boolean;
  videoListError: string | null;
  nextUuid: string | null;
  hasNextPage: boolean;
  progressMap: Record<string, number>;
  isLoadingMore: boolean;
}

const AIVideoPage: React.FC = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const hasImageUrl = urlParams.has('image_url');
  const imageUrlParam = hasImageUrl ? decodeURIComponent(urlParams.get('image_url')!) : null;

  const getInitialStep = (): 'welcome' | 'form' | 'videoList' => {
    if (hasImageUrl) return 'form';
    return isMobileDevice() ? 'welcome' : 'form';
  };

  const [state, setState] = useState<AIVideoState>({
    currentStep: getInitialStep(),
    rightPanelContent: 'welcome',
    uploadedImage: hasImageUrl ? [imageUrlParam] : [],
    positivePrompt: [],
    negativePrompt: '',
    multiple: true,
    quality: 'high',
    isGenerating: false,
    error: null,
    taskId: '',
    progress: 0,
    videoList: [],
    isLoadingVideos: true,
    hasVideos: false,
    videoListError: null,
    nextUuid: null,
    hasNextPage: false,
    progressMap: {},
    isLoadingMore: false,
  });

  const pollingTimersRef = useRef<Record<string, ReturnType<typeof setTimeout>>>({});
  const timeTrackerRef = useRef<PageTimeTracker | null>(null);
  const isUnmountedRef = useRef(false);

  const pageVisibilityRef = useRef<boolean>(true);
  const pollingStateRef = useRef<Record<string, {
    uuid: string;
    pollCount: number;
    isCurrentlyGenerating: boolean;
    lastPollTime: number;
    expectedNextPollTime: number;
  }>>({});
  const visibilityChangeHandlerRef = useRef<(() => void) | null>(null);

  // 使用 useQuotaUsage hook 管理额度数据
  const { quotaUsage, refreshQuotaUsage, updateUsedQuota } = useQuotaUsage('ai_material');

  // 使用 useFreeQuota hook 管理免费额度
  const {
    freeModalVisible,
    quotaAccessData,
    freeModalLoading,
    checkFreeQuota,
    handleFreeModalClose,
    handleFreeModalReceive: originalHandleFreeModalReceive,
  } = useFreeQuota({
    resourceKey: 'ai_material',
    eventPrefix: 'aigc_video',
    errorMessages: {
      obtainFailed: i18next.t('j-dingtalk-web_pages_ai-video_FailedToObtainFreeQuota'),
      interfaceAbnormal: i18next.t('j-dingtalk-web_pages_ai-video_TheInterfaceIsAbnormalPlease'),
      collectFailed: i18next.t('j-dingtalk-web_pages_ai-video_FailedToCollectPleaseTry'),
    },
  });

  // 包装 handleFreeModalReceive 来传递 refreshQuotaUsage 函数
  const handleFreeModalReceive = async () => {
    await originalHandleFreeModalReceive(refreshQuotaUsage);
  };

  // Page Visibility API handlers for iOS background support
  const handleVisibilityChange = () => {
    const isVisible = !document.hidden;
    pageVisibilityRef.current = isVisible;

    if (isVisible) {
      // Page became visible again, check for interrupted polling
      log('info', 'Page became visible, checking interrupted polling');
      checkAndResumeInterruptedPolling();
    } else {
      log('info', 'Page became hidden, polling may be interrupted');
    }
  };

  // Check and resume polling that was interrupted due to page visibility
  const checkAndResumeInterruptedPolling = () => {
    const currentTime = Date.now();
    let resumedCount = 0;

    Object.entries(pollingStateRef.current).forEach(([uuid, state]) => {
      if (currentTime > state.expectedNextPollTime + 1000) {
        log('info', `Resuming interrupted polling for video ${uuid}`);
        stopPollingForVideo(uuid);
        pollVideoStatus(uuid, state.pollCount, state.isCurrentlyGenerating);
        resumedCount++;
      }
    });

    if (resumedCount > 0) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationMonitoringResumed', {
          count: resumedCount,
        }),
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
    }
  };

  // Calculate progress based on time elapsed since video creation
  const calculateTimeBasedProgress = (createdAt: string): number => {
    try {
      const createdTime = new Date(createdAt).getTime();
      const currentTime = Date.now();
      const elapsedSeconds = (currentTime - createdTime) / 1000;

      const estimatedTotalSeconds = 300;

      let progress = (elapsedSeconds / estimatedTotalSeconds) * 100;

      progress = Math.max(2, Math.min(95, progress));

      return Math.round(progress);
    } catch {
      return 2;
    }
  };

  // Utility function to scroll to top of video list
  const scrollToTop = () => {
    setTimeout(() => {
      if (isMobileDevice()) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        const rightPanel = document.querySelector('.ai-video-right-panel') as HTMLElement;
        if (rightPanel) {
          rightPanel.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
    }, 100);
  };

  useEffect(() => {
    setPageTitle(i18next.t('j-dingtalk-web_pages_aiVideo_AiVideoGeneration'));

    checkFreeQuota();

    sendUT('aigc_video_homepage_exposure', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });

    timeTrackerRef.current = initPageTimeTracking('aigc_video_generate_time');

    visibilityChangeHandlerRef.current = handleVisibilityChange;
    document.addEventListener('visibilitychange', handleVisibilityChange);

    loadVideoList();

    return () => {
      isUnmountedRef.current = true;

      if (timeTrackerRef.current) {
        timeTrackerRef.current.cleanup();
        timeTrackerRef.current = null;
      }

      Object.values(pollingTimersRef.current).forEach((timer) => {
        clearTimeout(timer);
      });
      pollingTimersRef.current = {};

      pollingStateRef.current = {};

      if (visibilityChangeHandlerRef.current) {
        document.removeEventListener('visibilitychange', visibilityChangeHandlerRef.current);
        visibilityChangeHandlerRef.current = null;
      }
    };
  }, []);

  // Handle share button click
  const handleShareClick = () => {
    if (isDingTalk()) {
      $setShare({
        type: 0,
        url: window.location.href,
        title: i18next.t('j-dingtalk-web_pages_aiVideo_AiVideoGeneration'),
        content: i18next.t('j-dingtalk-web_pages_aiVideo_MakingHighQualityECommerce'),
        image:
          'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
      });
    }
  };

  // Handle create new video button click (for VideoList step)
  const handleCreateNewClick = () => {
    handleCreateNewVideo();
  };

  // Handle order list button click
  const handleOrderListClick = () => {
    const url = getOrderListUrl();
    if (isDingTalk()) {
      $openLink({
        url: openDualLink(url),
      });
    } else {
      window.open(url);
    }
  };

  // Dynamic navbar configuration based on current step
  const navbarConfig = useMemo(() => {
    switch (state.currentStep) {
      case 'welcome':
        return {
          showOrderButton: true,
          showShareButton: true,
          onOrderClick: handleOrderListClick,
          onShareClick: handleShareClick,
          shareConfig: {
            url: window.location.href,
            title: i18next.t('j-dingtalk-web_pages_aiVideo_AiVideoGeneration'),
            content: i18next.t('j-dingtalk-web_pages_aiVideo_MakingHighQualityECommerce'),
            image:
              'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
          },
        };
      case 'form':
        // VideoForm: show list button and share button
        return {
          customRightButtons: [
            {
              icon: <MoreOutlined />,
              text: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_Share'),
              onClick: handleShareClick,
            },
          ],
        };
      case 'videoList':
        // VideoList: show create button (edit icon)
        return {
          customRightButtons: [
            {
              icon: <EditOutlined />,
              text: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_Create'),
              onClick: handleCreateNewClick,
            },
          ],
        };
      default:
        // Default: show share button
        return {
          showShareButton: true,
          onShareClick: handleShareClick,
        };
    }
  }, [state.currentStep]);

  // Stop polling for a specific video
  const stopPollingForVideo = (uuid: string) => {
    if (pollingTimersRef.current[uuid]) {
      clearTimeout(pollingTimersRef.current[uuid]);
      delete pollingTimersRef.current[uuid];
    }

    // Also clean up polling state
    if (pollingStateRef.current[uuid]) {
      delete pollingStateRef.current[uuid];
    }
  };

  // Generic polling function for video status with iOS background support
  const pollVideoStatus = async (
    uuid: string,
    pollCount = 0,
    isCurrentlyGenerating = false,
  ): Promise<void> => {
    const maxPollCount = 60;
    const pollInterval = 5000;
    const currentTime = Date.now();

    try {
      const currentPollCount = pollCount + 1;

      pollingStateRef.current[uuid] = {
        uuid,
        pollCount: currentPollCount,
        isCurrentlyGenerating,
        lastPollTime: currentTime,
        expectedNextPollTime: currentTime + pollInterval,
      };

      let progressPercentage: number;

      setState((prev) => {
        const existingProgress = prev.progressMap[uuid] || 0;

        const videoInfo = prev.videoList.find((videoInfo) => videoInfo.uuid === uuid);
        if (videoInfo && videoInfo.createdAt) {
          const timeBasedProgress = calculateTimeBasedProgress(videoInfo.createdAt);
          progressPercentage = Math.max(existingProgress, timeBasedProgress);
        } else if (existingProgress && existingProgress > 2) {
          progressPercentage = Math.min(existingProgress + 1, 95);
        } else {
          progressPercentage = Math.min((currentPollCount / maxPollCount) * 90, 90);
        }

        if (currentPollCount >= maxPollCount) {
          progressPercentage = Math.max(95, existingProgress);
        } else {
          progressPercentage = Math.max(existingProgress, Math.min(progressPercentage, 95));
        }

        return {
          ...prev,
          progress: isCurrentlyGenerating ? progressPercentage : prev.progress,
          progressMap: {
            ...prev.progressMap,
            [uuid]: progressPercentage,
          },
        };
      });

      const result = await checkMultiVideoStatus({ uuid }) as CheckMultiVideoStatusResponse;

      if (result.status === 'finish' && result.datas && Array.isArray(result.datas)) {
        stopPollingForVideo(uuid);
        setState((prev) => {
          const newProgressMap = { ...prev.progressMap };
          delete newProgressMap[uuid];
          return {
            ...prev,
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            progress: isCurrentlyGenerating ? 100 : prev.progress,
            progressMap: newProgressMap,
            videoList: prev.videoList.map((videoInfoList) =>
              videoInfoList.uuid === uuid
                ? {
                    ...videoInfoList,
                    datas: (videoInfoList.datas || []).map((video, index) => {
                      const matched = result.datas[index];
                      if (matched) {
                        return {
                          ...video,
                          updatedAt: matched.finishTime?.toString(),
                          status: matched.status || 'finish',
                          videoUrl: matched.videoUrl || video.videoUrl,
                          duration: matched.duration || video.duration || 5,
                          requestId: matched.requestId || video.requestId,
                        } as typeof video;
                      }
                      return video;
                    }),
                    status: 'finish' as const,
                    updatedAt: result.finishTime?.toString() || new Date().toISOString(),
                  }
                : videoInfoList
            ),
          };
        });

        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationCompleted'),
          position: 'top',
          maskClickable: true,
          duration: 2,
        });
        return;
      }

      // Check if status is failed
      if (result.status === 'failed') {
        stopPollingForVideo(uuid);
        setState((prev) => {
          const newProgressMap = { ...prev.progressMap };
          delete newProgressMap[uuid];

          return {
            ...prev,
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            error: isCurrentlyGenerating
              ? i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationFailed')
              : prev.error,
            progress: isCurrentlyGenerating ? 0 : prev.progress,
            progressMap: newProgressMap,
            videoList: prev.videoList.map((videoInfoList) =>
              videoInfoList.uuid === uuid
                ? {
                    ...videoInfoList,
                    datas: (videoInfoList.datas || []).map((video, index) => {
                      const matched = result.datas[index];
                      if (matched) {
                        return {
                          ...video,
                          updatedAt: matched.finishTime?.toString(),
                          status: matched.status || 'failed',
                          videoUrl: matched.videoUrl || video.videoUrl,
                          duration: matched.duration || video.duration || 5,
                          requestId: matched.requestId || video.requestId,
                        } as typeof video;
                      }
                      return video;
                    }),
                    status: 'failed' as const,
                    updatedAt: result.finishTime?.toString() || new Date().toISOString(),
                  }
                : videoInfoList
            ),
          };
        });
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationFailedPleaseTry'),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
        return;
      }

      if (result.status === 'pending' || result.status === 'processing') {
        if (currentPollCount >= maxPollCount) {
          setState((prev) => {
            return {
              ...prev,
              progress: isCurrentlyGenerating ? 95 : prev.progress,
              progressMap: {
                ...prev.progressMap,
                [uuid]: 95,
              },

              videoList: prev.videoList.map((videoInfoList) =>
                videoInfoList.uuid === uuid
                  ? {
                      ...videoInfoList,
                      datas: (videoInfoList.datas || []).map((video, index) => {
                        const matched = result.datas[index];
                        if (matched) {
                          return {
                            ...video,
                            updatedAt: matched.finishTime?.toString(),
                            status: matched.status || 'processing',
                            videoUrl: matched.videoUrl || video.videoUrl,
                            duration: matched.duration || video.duration || 5,
                            requestId: matched.requestId || video.requestId,
                          } as typeof video;
                        }
                        return video;
                      }),
                      status: 'processing' as const,
                      updatedAt: result.finishTime?.toString() || new Date().toISOString(),
                    }
                  : videoInfoList
              ),
            };
          });

          if (currentPollCount === maxPollCount) {
            Toast.info({
              content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationTakingLongerThan'),
              position: 'top',
              maskClickable: true,
              duration: 3,
            });
          }
        }

        if (!isUnmountedRef.current) {
          const scheduleNextPoll = () => {
            if (isUnmountedRef.current) return;

            const adjustedInterval = pageVisibilityRef.current ? pollInterval : Math.min(pollInterval * 3, 30000); // Max 30 seconds when hidden

            const timer = setTimeout(() => {
              if (!isUnmountedRef.current) {
                pollVideoStatus(uuid, currentPollCount, isCurrentlyGenerating);
              }
            }, adjustedInterval);

            pollingTimersRef.current[uuid] = timer;
          };

          scheduleNextPoll();
        }
        return;
      }

      // Handle unexpected status
      stopPollingForVideo(uuid);
      setState((prev) => {
        const existingProgress = prev.progressMap[uuid] || 0;
        const newProgressMap = { ...prev.progressMap };
        delete newProgressMap[uuid];
        return {
          ...prev,
          isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
          error: isCurrentlyGenerating
            ? i18next.t('j-dingtalk-web_pages_aiVideo_UnknownStatusResultstatus', {
              resultStatus: result.status,
            })
            : prev.error,
          // Keep progress at current level for unexpected status, don't reset to 0
          progress: isCurrentlyGenerating ? Math.max(existingProgress, 95) : prev.progress,
          progressMap: newProgressMap,
          // Update the video status in the list
          videoList: prev.videoList.map((video) =>
            (video.uuid === uuid
              ? {
                ...video,
                status: 'failed' as const,
                updatedAt: result.finishTime?.toString() || new Date().toISOString()
                }
              : video
            )
          ),
        };
      });

      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_AnUnknownErrorOccurredWhile'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    } catch (error) {
      stopPollingForVideo(uuid);
      setState((prev) => {
        const existingProgress = prev.progressMap[uuid] || 0;
        const newProgressMap = { ...prev.progressMap };
        
        delete newProgressMap[uuid];
        
        const errorMessage =
          error instanceof Error
            ? error.message
            : i18next.t('j-dingtalk-web_pages_aiVideo_QueryStatusFailed');
        return {
          ...prev,
          isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
          error: isCurrentlyGenerating ? errorMessage : prev.error,
          progress: isCurrentlyGenerating ? Math.max(existingProgress, 95) : prev.progress,
          progressMap: newProgressMap,
          videoList: prev.videoList.map((videoInfoList) =>
            videoInfoList.uuid === uuid
              ? {
                  ...videoInfoList,
                  status: 'failed' as const,
                  updatedAt: new Date().toISOString(),
                }
              : videoInfoList
          ),
        };
      });

      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_FailedToQueryTheVideo'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Start polling for a specific video
  const startPollingForVideo = (uuid: string, isCurrentlyGenerating = false) => {
    if (pollingTimersRef.current[uuid]) {
      return;
    }

    pollingStateRef.current[uuid] = {
      uuid,
      pollCount: 0,
      isCurrentlyGenerating,
      lastPollTime: Date.now(),
      expectedNextPollTime: Date.now() + 5000,
    };

    pollVideoStatus(uuid, 0, isCurrentlyGenerating);
  };

  const checkAndStartPollingForVideos = (videoList: VideoInfoList[]) => {
    videoList.forEach((videoItem) => {
      if (
        (videoItem.status === 'pending' || videoItem.status === 'processing') &&
        !pollingTimersRef.current[videoItem.uuid]
      ) {
        startPollingForVideo(videoItem.uuid);
        }
    });
  };

  // Load video list from API
  const loadVideoList = async () => {
    setState((prev) => ({ ...prev, isLoadingVideos: true, videoListError: null }));

    try {
      let result;
      try {
        result = await listMultiVideos({ limit: 20 }) as ListVideoResponse;
      } catch (error: any) {
        log('error', error.message);
      }

      if (result?.videoInfos?.length > 0) {
        const videoInfos = JSON.parse(result.videoInfos) || [];
        const videoList: VideoInfoList[] = [];

        videoInfos.forEach((group: any) => {
          if (group.datas && Array.isArray(group.datas)) {
            const processedDatas: VideoInfo[] = group.datas.map((item: any) => {
              let imageUrl = '';
              try {
                const refImageInfo = JSON.parse(item.refImageInfo || '{}');
                imageUrl = refImageInfo.url || '';
              } catch (e: any) {
                imageUrl = '';
                log('error', e.message);
              }

              return {
                videoUuid: item.videoUuid,
                videoUrl: item.videoUrl || '',
                gifUrl: item.gifUrl || '',
                imageUrl,
                positivePrompt: item.requirements || '',
                negativePrompt: item.restrictions || '',
                quality: item.quality || 'high',
                duration: item.duration || 5,
                userRating: item.userRating || '0',
                status: item.status,
                requestId: item.requestId,
                updatedAt: item.updatedAt || item.videoFinishTime,
              };
            });

            videoList.push({
              uuid: group.uuid,
              createdAt: group.createdAt,
              updatedAt: group.updatedAt,
              operator: group.operator,
              userRating: group.userRating,
              mergedVideoKey: group.mergedVideoKey,
              markedVideoKey: group.markedVideoKey,
              taskMode: group.taskMode || 'SINGLE',
              datas: processedDatas,
              status: group.status,
            });
          }
        });

        const hasVideos = videoList.length > 0;
        const isMobile = isMobileDevice();

        const initialProgressMap: Record<string, number> = {};
        videoList.forEach((videoItem) => {
          if (videoItem.status === 'processing' && videoItem.createdAt) {
            initialProgressMap[videoItem.uuid] = calculateTimeBasedProgress(videoItem.createdAt);
          }
        });

        const urlHasImageParam = new URLSearchParams(window.location.search).has('image_url');
        let initialStep: AIVideoState['currentStep'];
        if (urlHasImageParam) {
          initialStep = 'form';
        } else if (isMobile) {
          initialStep = hasVideos ? 'videoList' : 'welcome';
        } else {
          initialStep = 'form';
        }

        setState((prev) => ({
          ...prev,
          videoList,
          hasVideos,
          isLoadingVideos: false,
          nextUuid: result.nextUuid || null,
          hasNextPage: result.hasNext || false,
          progressMap: { ...prev.progressMap, ...initialProgressMap },
          currentStep: initialStep,
          rightPanelContent: hasVideos ? 'videoList' : 'welcome',
        }));

        if (hasVideos) {
          scrollToTop();
        }

        checkAndStartPollingForVideos(videoList);
      } else {
        const urlHasImageParam = new URLSearchParams(window.location.search).has('image_url');
        setState((prev) => ({
          ...prev,
          videoList: [],
          hasVideos: false,
          isLoadingVideos: false,
          nextUuid: null,
          hasNextPage: false,
          currentStep: urlHasImageParam || !isMobileDevice() ? 'form' : 'welcome',
          rightPanelContent: 'welcome',
        }));
      }
    } catch (error) {
      const urlHasImageParam = new URLSearchParams(window.location.search).has('image_url');
      setState((prev) => ({
        ...prev,
        isLoadingVideos: false,
        videoListError:
          error instanceof Error
            ? error.message
            : i18next.t('j-dingtalk-web_pages_aiVideo_FailedToObtainTheVideo'),
        hasVideos: false,
        currentStep: urlHasImageParam ? 'form' : (isMobileDevice() ? 'welcome' : 'form'),
        rightPanelContent: 'welcome',
      }));
    }
  };

  // Load more videos for pagination
  const loadMoreVideos = async () => {
    // Don't load if already loading or no more pages
    if (state.isLoadingMore || !state.hasNextPage || !state.nextUuid) {
      return;
    }

    setState((prev) => ({ ...prev, isLoadingMore: true }));

    try {
      const result = await listMultiVideos({
        limit: 20,
        nextUuid: state.nextUuid,
      }) as any;

      if (result?.videoInfos?.length > 0) {
        const videoInfos = JSON.parse(result.videoInfos) || [];
        const newVideoList: VideoInfoList[] = [];

        videoInfos.forEach((group: any) => {
          if (group.datas && Array.isArray(group.datas)) {
            const processedDatas: VideoInfo[] = group.datas.map((item: any) => {
              let imageUrl = '';
              try {
                const refImageInfo = JSON.parse(item.refImageInfo || '{}');
                imageUrl = refImageInfo.url || '';
              } catch (e: any) {
                imageUrl = '';
                log('error', e.message);
              }

              return {
                videoUuid: item.videoUuid,
                videoUrl: item.videoUrl || '',
                gifUrl: item.gifUrl || '',
                imageUrl,
                positivePrompt: item.requirements || '',
                negativePrompt: item.restrictions || '',
                quality: item.quality || 'high',
                duration: item.duration || 5,
                userRating: item.userRating || '0',
                status: item.status,
                requestId: item.requestId,
                updatedAt: item.updatedAt || item.videoFinishTime,
              };
            });

            newVideoList.push({
              uuid: group.uuid,
              createdAt: group.createdAt,
              updatedAt: group.updatedAt,
              operator: group.operator,
              userRating: group.userRating,
              mergedVideoKey: group.mergedVideoKey,
              markedVideoKey: group.markedVideoKey,
              taskMode: group.taskMode || 'SINGLE',
              datas: processedDatas,
              status: group.status,
            });
          }
        });

        setState((prev) => ({
          ...prev,
          videoList: [...prev.videoList, ...newVideoList],
          nextUuid: result.nextUuid || null,
          hasNextPage: result.hasNext || false,
          isLoadingMore: false,
        }));

        checkAndStartPollingForVideos(newVideoList);
      } else {
        setState((prev) => ({
          ...prev,
          hasNextPage: false,
          isLoadingMore: false,
        }));
      }
    } catch (error: any) {
      log('error', error);
      setState((prev) => ({
        ...prev,
        hasNextPage: false,
        nextUuid: null,
        isLoadingMore: false,
      }));
    }
  };

  // Handle step navigation
  const handleStepChange = (
    step: AIVideoState['currentStep'],
    e?: React.MouseEvent<HTMLDivElement>,
  ) => {
    e?.preventDefault();
    e?.stopPropagation();
    setState((prev) => ({ ...prev, currentStep: step }));
  };

  // Handle form data update
  const handleFormUpdate = (updates: Partial<AIVideoState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  // Handle create new video action
  const handleCreateNewVideo = () => {
    setState((prev) => ({
      ...prev,
      uploadedImage: [],
      positivePrompt: [],
      negativePrompt: '',
      quality: 'high',
      error: null,
      currentStep: isMobileDevice() ? 'form' : prev.currentStep,
    }));
  };

  // Handle refresh video list
  const handleRefreshVideoList = () => {
    loadVideoList();
    scrollToTop();
  };

  // Optimized single video update after regeneration
  const handleOptimizedVideoUpdate = async (regenerateResult: any, originalUuid: string) => {
    try {
      if (regenerateResult.uuid && regenerateResult.uuid !== originalUuid) {
        // Create new video info object for the regenerated video
        const currentTime = new Date().toISOString();
        let originalVideo: VideoInfo | undefined;
        for (const videoInfoList of state.videoList) {
          const found = videoInfoList.datas.find((v) => v.videoUuid === originalUuid);
          if (found) {
            originalVideo = found;
            break;
          }
        }

        if (originalVideo) {
          const newVideoInfo: VideoInfo = {
            videoUuid: regenerateResult.uuid,
            videoUrl: '',
            imageUrl: originalVideo.imageUrl,
            positivePrompt: originalVideo.positivePrompt,
            negativePrompt: originalVideo.negativePrompt,
            quality: originalVideo.quality,
            duration: originalVideo.duration,
            status: 'processing',
            requestId: regenerateResult.uuid,
            createdAt: currentTime,
          };

          const newVideoInfoList: VideoInfoList = {
            uuid: regenerateResult.uuid,
            createdAt: currentTime,
            updatedAt: currentTime,
            taskMode: 'SINGLE',
            datas: [newVideoInfo],
            status: 'processing',
          };

          setState((prev) => ({
            ...prev,
            videoList: [newVideoInfoList, ...prev.videoList],
          }));

          startPollingForVideo(regenerateResult.uuid);
          return;
        }
      }

      try {
        const statusResult = await checkMultiVideoStatus({ uuid: originalUuid }) as CheckMultiVideoStatusResponse;

        if (statusResult.success) {
          if (statusResult.status === 'finish' && statusResult.datas) {
            statusResult.datas.forEach((videoData: any) => {
              const requestId = videoData.requestId;

              setState((prev) => ({
                ...prev,
                videoList: prev.videoList.map((videoInfoList) => ({
                  ...videoInfoList,
                  datas: videoInfoList.datas.map((video) =>
                    video.requestId === requestId
                      ? {
                          ...video,
                          status: videoData.status || 'processing',
                          videoUrl: videoData.videoUrl || video.videoUrl,
                          duration: videoData.duration || video.duration || 5,
                        }
                      : video,
                  ),
                })),
              }));
            });
          }

          if (statusResult.status === 'pending' || statusResult.status === 'processing') {
            startPollingForVideo(originalUuid);
          }
        } else {
          loadVideoList();
        }
      } catch (error: any) {
        loadVideoList();
        log('error', error.message);
      }
    } catch (error: any) {
      loadVideoList();
      log('error', error.message);
    }
  };

  // Handle regenerate video from list
  const handleRegenerateFromList = (videoInfoList: VideoInfoList) => {
    // Extract data from the video list
    const firstVideo = videoInfoList.datas[0];
    const allImages = videoInfoList.datas.map(video => video.imageUrl);
    const allPrompts = videoInfoList.datas.map(video => video.positivePrompt);

    setState((prev) => ({
      ...prev,
      // For multi-video mode, use arrays; for single video, use the first item
      uploadedImage: videoInfoList.taskMode === 'MULTI' ? allImages : firstVideo.imageUrl,
      positivePrompt: videoInfoList.taskMode === 'MULTI' ? allPrompts : firstVideo.positivePrompt,
      negativePrompt: firstVideo.negativePrompt, // Use first video's negative prompt
      quality: firstVideo.quality, // Use first video's quality
      multiple: true, // Set multiple mode based on task mode
      currentStep: 'form',
      rightPanelContent: state.hasVideos ? 'videoList' : 'welcome',
    }));
  };

  // Handle video generation
  const handleGenerateVideo = async () => {
    sendUT('aigc_video_generate_click', {
      device: isMobileDevice() ? 'mobile' : 'pc',
      quality: state.quality,
      duration: 5,
      positivePrompt: state.positivePrompt,
      negativePrompt: state.negativePrompt,
      image: state.uploadedImage,
    });

    if (state.isGenerating) {
      return;
    }

    // Check if user has enough quota (at least 2 times)
    if (quotaUsage && ((quotaUsage.totalQuota || 0) - (quotaUsage.usedQuota || 0)) < 2) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_InsufficientQuotaPleaseRecharge'),
        position: 'top',
        maskClickable: true,
        duration: 3,
        onClose: () => {
          const url = getCreateOrderUrl();
          if (isDingTalk()) {
            $openLink({
              url: openDualLink(url),
            });
          } else {
            window.open(url);
          }
        },
      });
      return;
    }

    // 构造多图参数数组
    let videoRequests: GenerateMultiVideoRequest[] = [];

    if (state.multiple) {
      // 多图模式：为每张图片构造一个请求对象
      const currentImages = Array.isArray(state.uploadedImage) ? state.uploadedImage : [];
      const currentPrompts = Array.isArray(state.positivePrompt) ? state.positivePrompt : [];

      // 如果有图片，为每张图片构造请求；如果没有图片但有提示词，也构造一个请求
      const requestCount = Math.max(currentImages.length, 1);

      for (let i = 0; i < requestCount; i++) {
        const imageUrl = currentImages[i] || null;
        const prompt = currentPrompts[i] || '';

        // 只有有图片或有提示词的情况下才添加请求
        if (imageUrl && prompt.trim()) {
          videoRequests.push({
            imageInfo: JSON.stringify({
              url: imageUrl,
            }),
            requirements: prompt,
            restrictions: state.negativePrompt,
            quality: state.quality,
            duration: 5,
          });
        }
      }
    } else {
      // 单图模式：构造单个请求
      const currentImage = typeof state.uploadedImage === 'string' ? state.uploadedImage : null;
      const currentPrompt = typeof state.positivePrompt === 'string' ? state.positivePrompt : '';

      if (currentImage && currentPrompt.trim()) {
        videoRequests.push({
          imageInfo: JSON.stringify({
            url: currentImage,
          }),
          requirements: currentPrompt,
          restrictions: state.negativePrompt,
          quality: state.quality,
          duration: 5,
        });
      }
    }

    // 验证是否有有效的请求
    if (videoRequests.length === 0) {
      Toast.fail({
        content: state.multiple
          ? i18next.t('j-dingtalk-web_pages_aiVideo_PleaseUploadImagesAndEnterPrompts')
          : i18next.t('j-dingtalk-web_pages_aiVideo_PleaseUploadAnImage'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    setState((prev) => ({
      ...prev,
      isGenerating: true,
      error: null,
      progress: 0,
    }));

    // 获取第一个有效请求用于埋点
    const firstRequest = videoRequests[0];

    try {
      // Call video generation API
      const result = await generateMultiVideo({
        datas: videoRequests,
      }) as any;

      if (result.success && result.uuid) {
        sendUT('aigc_video_generate_success', {
          device: isMobileDevice() ? 'mobile' : 'pc',
          quality: state.quality,
          duration: 5,
          positivePrompt: state.positivePrompt,
          negativePrompt: state.negativePrompt,
          image: state.uploadedImage,
        });

        // Create new video info objects for processing state (multiple videos)
        const currentTime = new Date().toISOString();
        const newVideoInfos: VideoInfo[] = videoRequests.map((request) => ({
          videoUuid: result.uuid,
          videoUrl: '',
          imageUrl: JSON.parse(request.imageInfo)?.url || '',
          positivePrompt: request.requirements,
          negativePrompt: request.restrictions,
          quality: request.quality,
          duration: request.duration || 5,
          status: 'processing',
          requestId: result.uuid,
          createdAt: currentTime,
        }));

        // Create new VideoInfoList for the generated videos
        const newVideoInfoList: VideoInfoList = {
          uuid: result.uuid,
          createdAt: currentTime,
          updatedAt: currentTime,
          taskMode: 'MULTI',
          datas: newVideoInfos,
          status: 'processing',
        };

        // Calculate initial progress for the new videos
        const initialProgress = calculateTimeBasedProgress(currentTime);

        setState((prev) => ({
          ...prev,
          taskId: result.uuid,
          videoList: [newVideoInfoList, ...prev.videoList],
          hasVideos: true,
          progressMap: {
            ...prev.progressMap,
            [result.uuid]: initialProgress,
          },
          currentStep: isMobileDevice() ? 'videoList' : 'form',
          rightPanelContent: 'videoList',
        }));

        scrollToTop();

        startPollingForVideo(result.uuid, true);

        updateUsedQuota(2);
      } else {
        sendUT('aigc_video_generate_fail', {
          device: isMobileDevice() ? 'mobile' : 'pc',
          quality: state.quality,
          duration: 5,
          positivePrompt: firstRequest.requirements,
          negativePrompt: firstRequest.restrictions,
          image: JSON.parse(firstRequest.imageInfo).url,
        });

        throw new Error(
          result?.errorMsg || i18next.t('j-dingtalk-web_pages_aiVideo_FailedToGenerate'),
        );
      }
    } catch (error) {
      sendUT('aigc_video_generate_fail', {
        device: isMobileDevice() ? 'mobile' : 'pc',
        quality: state.quality,
        duration: 5,
        positivePrompt: firstRequest.requirements,
        negativePrompt: firstRequest.restrictions,
        image: JSON.parse(firstRequest.imageInfo).url,
      });

      setState((prev) => ({
        ...prev,
        isGenerating: false,
        error:
          error instanceof Error
            ? error.message
            : i18next.t('j-dingtalk-web_pages_aiVideo_FailedToGeneratePleaseTry'),
        progress: 0,
      }));

      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationFailedPleaseTry'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Render PC layout with left-right structure
  const renderPCLayout = () => {
    // Show loading overlay if videos are loading
    if (state.isLoadingVideos) {
      return (
        <div className="ai-video-pc-layout">
          <Loading text={i18next.t('j-dingtalk-web_pages_aiVideo_Loading')} />
        </div>
      );
    }

    return (
      <div className="ai-video-pc-layout">
        {/* Left Panel - Always show VideoForm */}
        <div className="ai-video-left-panel">
          <VideoForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateVideo}
            quotaUsage={quotaUsage}
            multiple={state.multiple}
          />
        </div>

        {/* Right Panel - Show Welcome, VideoList, or Preview based on state */}
        <div className="ai-video-right-panel">
          {state.rightPanelContent === 'welcome' && (
            <WelcomeScreen
              quotaUsage={quotaUsage}
              hasGenerateBtn={false}
              onOrderClick={handleOrderListClick}
            />
          )}

          {state.rightPanelContent === 'videoList' && (
            <VideoList
              videoList={state.videoList}
              isLoading={false} // Loading is handled at page level
              error={state.videoListError}
              onCreateNew={handleCreateNewVideo}
              onRegenerate={handleRegenerateFromList}
              onRefresh={handleRefreshVideoList}
              progressMap={state.progressMap}
              hasNextPage={state.hasNextPage}
              isLoadingMore={state.isLoadingMore}
              onLoadMore={loadMoreVideos}
              loadVideoList={loadVideoList}
              onOptimizedVideoUpdate={handleOptimizedVideoUpdate}
            />
          )}
        </div>
      </div>
    );
  };

  // Render mobile layout (updated with video list support)
  const renderMobileLayout = () => {
    // Show loading screen if videos are loading
    if (state.isLoadingVideos) {
      return <Loading text={i18next.t('j-dingtalk-web_pages_aiVideo_Loading')} />;
    }

    switch (state.currentStep) {
      case 'welcome':
        return (
          <WelcomeScreen
            hasGenerateBtn
            quotaUsage={quotaUsage}
            onGetStarted={(e) => handleStepChange('form', e)}
          />
        );

      case 'form':
        return (
          <VideoForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateVideo}
            quotaUsage={quotaUsage}
            multiple={state.multiple}
          />
        );

      case 'videoList':
        return (
          <VideoList
            videoList={state.videoList}
            isLoading={false} // Loading is handled at page level
            error={state.videoListError}
            onCreateNew={handleCreateNewVideo}
            onRegenerate={handleRegenerateFromList}
            onRefresh={handleRefreshVideoList}
            progressMap={state.progressMap}
            hasNextPage={state.hasNextPage}
            isLoadingMore={state.isLoadingMore}
            onLoadMore={loadMoreVideos}
            loadVideoList={loadVideoList}
            onOptimizedVideoUpdate={handleOptimizedVideoUpdate}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* Mobile Navigation Bar - Fixed at the top, outside of the main container */}
      <MobileNavbar {...navbarConfig} />

      <div className="ai-video-page">
        {isMobileDevice() ? renderMobileLayout() : renderPCLayout()}

        {/* Free Modal */}
        <FreeModal
          visible={freeModalVisible}
          quotaAccessData={quotaAccessData}
          onClose={handleFreeModalClose}
          onReceive={handleFreeModalReceive}
          loading={freeModalLoading}
        />
      </div>
    </>
  );
};

export default AIVideoPage;
