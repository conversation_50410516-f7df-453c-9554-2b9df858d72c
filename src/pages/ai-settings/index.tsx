import React, { useMemo, useEffect } from 'react';
import { isDingTalk, setPageTitle, isMobileDevice } from '@/utils/jsapi';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import MobileNavbar from '@/components/MobileNavbar';
import { sendUT } from '@/utils/trace';
import './index.less';

const AISettingsPage: React.FC = () => {
  useEffect(() => {
    setPageTitle('设置');

    sendUT('aigc_settings_exposure', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });
  }, []);

  const navbarConfig = useMemo(() => ({
    showShareButton: true,
    onShareClick: () => {
      if (isDingTalk()) {
        $setShare({
          type: 0,
          url: window.location.href,
          title: document.title,
          content: '',
          image: '',
        });
      }
    },
    shareConfig: {
      url: window.location.href,
    },
  }), []);
  return (
    <>
      {/* Mobile Navigation Bar - Fixed at the top, outside of the main container */}
      <MobileNavbar {...navbarConfig} />
      <div className="ai-settings-page">
        AISettingsPage
      </div>
    </>
  );
};

export default AISettingsPage;
