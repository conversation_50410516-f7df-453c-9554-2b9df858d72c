import React, { useEffect, useState } from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { setPageTitle } from '@/utils/jsapi';
import { CheckCircleOutlined } from '@ali/ding-icons';
import { Toast } from 'dingtalk-design-mobile';
import theme, { IThemeType } from 'dingtalk-theme';
import MobileNavbar from '@/components/MobileNavbar';
import { createOrder, CreateResourceOrderRequest, checkRejectedOrder, CheckRejectedOrderRequest } from '@/apis/quota';
import useQuotaUsage from '@/hooks/useQuotaUsage';
import QuotaDisplay from '@/components/QuotaDisplay';
import Loading from '@/components/Loading';
import { getOrderListUrl } from '@/utils/env';
import { log } from '@/utils/console';
import { useDebounce } from '@/hooks/useDebounce';
import './index.less';

theme.setTheme(IThemeType.dark);

// Product data interface
interface ProductInfo {
  credits: number;
  videoCredits: number;
  discountPrice: number;
  taxPrice: number;
  totalPrice: number;
  currency: string;
}

// Mock product data based on the design
const PRODUCT_DATA: ProductInfo = {
  credits: 200,
  videoCredits: 100,
  discountPrice: 2000,
  taxPrice: 200,
  totalPrice: 2200,
  currency: i18next.t('j-dingtalk-web_pages_create-order_Currency'),
};

// Fixed item code for this product
const ITEM_CODE = 'DT_GOODS_MATERIAL_101001';

const CreateOrder: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasRejectedOrder, setHasRejectedOrder] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);

  // 使用 useQuotaUsage hook 管理额度数据
  const { quotaUsage, refreshQuotaUsage } = useQuotaUsage('ai_material');

  useEffect(() => {
    // Set page title for mobile
    setPageTitle(i18next.t('j-dingtalk-web_pages_create-order_Title'));

    // Check rejected order status
    checkRejectedOrderStatus();
  }, []);

  // Check rejected order status
  const checkRejectedOrderStatus = async () => {
    try {
      const request: CheckRejectedOrderRequest = {
        resourceKey: 'ai_material',
      };

      const response = await checkRejectedOrder(request);

      if (response.success) {
        setHasRejectedOrder(response.hasRejectedOrder);
      }
    } catch (error) {
      log.error('Failed to check rejected order status:', error);
    } finally {
      // 无论成功还是失败，都结束页面加载状态
      setIsPageLoading(false);
    }
  };

  // Handle payment button click (original function)
  const handlePaymentClickOriginal = async () => {
    // Prevent multiple clicks when already loading
    if (isLoading) {
      return;
    }

    setIsLoading(true);

    try {
      // Prepare order request data
      const orderRequest: CreateResourceOrderRequest = {
        itemCode: ITEM_CODE,
        resourceKey: 'ai_material',
      };

      // Create order
      const response = await createOrder(orderRequest) as any;

      if (response.success) {
        const { orderUuid: newOrderUuid } = response.data;
        // Handle different scenarios based on quotaGranted
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_create-order_TheOrderHasBeenGenerated'),
          position: 'top',
          duration: 2,
          maskClickable: true,
        });

        setIsLoading(false);
        refreshQuotaUsage();
        const orderDetailUrl = `${getOrderListUrl()}&orderUuid=${newOrderUuid}&ddtab=true`;
        window.location.replace(orderDetailUrl);
      } else {
        setIsLoading(false);
        // Handle order creation failure
        Toast.fail({
          content:
            response.errorMsg ||
            i18next.t('j-dingtalk-web_pages_create-order_OrderGenerationFailedPleaseTry'),
          position: 'top',
          duration: 3,
          maskClickable: true,
          icon: 'error',
        });
      }
    } catch (error) {
      setIsLoading(false);
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_create-order_OrderGenerationFailedPleaseTry'),
        position: 'top',
        duration: 3,
        maskClickable: true,
      });
    }
  };

  // Create debounced version of payment click handler
  const handlePaymentClick = useDebounce(handlePaymentClickOriginal, 500);

  // Feature list data
  const features = [
    {
      key: 'feature1',
      text: i18next.t('j-dingtalk-web_pages_create-order_Feature1'),
    },
    {
      key: 'feature2',
      text: i18next.t('j-dingtalk-web_pages_create-order_Feature2'),
    },
    {
      key: 'feature3',
      text: i18next.t('j-dingtalk-web_pages_create-order_Feature3'),
    },
  ];

  // Format price with currency
  const formatPrice = (price: number): string => {
    return `${price}${PRODUCT_DATA.currency}`;
  };

  // Generate description text with interpolation
  const getDescriptionText = (): string => {
    return i18next.t('j-dingtalk-web_pages_create-order_Description', {
      videoCount: PRODUCT_DATA.videoCredits,
    });
  };

  // 如果页面还在加载中，显示 Loading 组件
  if (isPageLoading) {
    return (
      <div className="create-order-page">
        <Loading text={i18next.t('j-dingtalk-web_components_Loading_Loading')} />
      </div>
    );
  }

  return (
    <>
      {/* Mobile Navigation Bar - Fixed at the top, outside of the main container */}
      <MobileNavbar
        title={i18next.t('j-dingtalk-web_pages_create-order_Title')}
        showOrderButton={false}
        showShareButton
      />

      <div className="create-order-page">
        <div className="content-wrapper">
          {/* Header Section */}
          <div className="header-section">
            <h1 className="page-title">{i18next.t('j-dingtalk-web_pages_create-order_Title')}</h1>
            <div className="usage-count">
              <span className="usage-label">
                {i18next.t('j-dingtalk-web_pages_create-order_UsageCount')}：
                <QuotaDisplay quotaUsage={quotaUsage} placeholder="0" />
              </span>
            </div>
          </div>

          {/* Main Product Card */}
          <div className="main-card">
            <div className="card-content">
              {/* Credits Display */}
              <div className="credits-number">
                {PRODUCT_DATA.credits}
                <span className="credits-unit">
                  {i18next.t('j-dingtalk-web_pages_create-order_Credits')}
                </span>
              </div>

              {/* Description */}
              <p className="credits-description">
                {PRODUCT_DATA.credits}
                {getDescriptionText()}
              </p>

              <div className="feature-divider" />

              {/* Features List */}
              <ul className="features-list">
                {features.map((feature) => (
                  <li key={feature.key} className="feature-item">
                    <CheckCircleOutlined className="feature-icon" />
                    <p className="feature-text">{feature.text}</p>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="footer-section">
          {/* Price Section */}
          <div className="price-section">
            <div className="price-row">
              <span className="price-label">
                {i18next.t('j-dingtalk-web_pages_create-order_ProductPrice')}
              </span>
              <div className="price-value">
                <span className="discount-tag">
                  {i18next.t('j-dingtalk-web_pages_create-order_DiscountPrice')}
                </span>
                <span className="discount-price">{formatPrice(PRODUCT_DATA.discountPrice)}</span>
              </div>
            </div>
            <div className="price-row">
              <span className="price-label">
                {i18next.t('j-dingtalk-web_pages_create-order_TaxPrice')}
              </span>
              <div className="price-value">
                <span className="discount-price">{formatPrice(PRODUCT_DATA.taxPrice)}</span>
              </div>
            </div>
            <div className="price-row">
              <span className="price-label">
                {i18next.t('j-dingtalk-web_pages_create-order_TotalPrice')}
              </span>
              <div className="price-value">
                <span className="discount-price">{formatPrice(PRODUCT_DATA.totalPrice)}</span>
              </div>
            </div>
          </div>

          {/* Action Button */}
          <button
            className={`action-button ${isLoading ? 'disabled' : ''}`}
            onClick={handlePaymentClick}
            disabled={isLoading}
          >
            {isLoading
              ? i18next.t('j-dingtalk-web_pages_create-order_LiZhong')
              : (hasRejectedOrder ? i18next.t('j-dingtalk-web_pages_create-order_Payment') : i18next.t('j-dingtalk-web_pages_create-order_PaymentButton'))
            }
          </button>
        </div>
      </div>
    </>
  );
};

export default CreateOrder;
