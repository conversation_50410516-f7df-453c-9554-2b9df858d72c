import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, To<PERSON>, Config<PERSON><PERSON>ider, Drawer } from 'dingtalk-design-mobile';
import { Popover } from 'dingtalk-design-desktop';
import { isMobileDevice } from '@/utils/jsapi';
import {
  InformationThereOutlined,
  CopyOutlined,
  CheckCircleFilled,
  CloseCircledFilled,
} from '@ali/ding-icons';
import ImageUploader from '@/components/ImageUploader';
import { BackgroundOption } from '@/common/types';
import BackgroundSelector from '../BackgroundSelector';
import SizeSelector from '../SizeSelector';
import { extractImage } from '@/apis/image';
import { sendUT } from '@/utils/trace';
import { log } from '@/utils/console';
import { QuotaUsageResponse } from '@/apis/quota';
import QuotaDisplay from '@/components/QuotaDisplay';
// import CompositionEditor from '@/components/CompositionEditor';
import './index.less';

interface HelpContentItem {
  type: 'text' | 'image' | 'section' | 'examples';
  text?: string;
  src?: string;
  alt?: string;
  title?: string;
  items?: string[];
  status?: 'correct' | 'incorrect';
  isTwoImage?: boolean;
  images?: Array<{
    src: string;
    alt?: string;
    label: string;
  }>;
}

// Help content interface
interface HelpContent {
  title: string;
  content: HelpContentItem[];
}

interface HelpContents {
  [key: string]: HelpContent;
}

interface ImageHelpProps {
  title: string;
  helpKey?: string;
  onlyTitle?: boolean;
}

// ImageForm props interface
interface ImageFormProps {
  state: {
    uploadedImage: string | null;
    positivePrompt: string;
    imageSize: string;
    isGenerating: boolean;
    error: string | null;
  };
  onUpdate: (
    updates: Partial<{
      uploadedImage: string | null;
      positivePrompt: string;
      imageSize: string;
      isGenerating: boolean;
      error: string | null;
    }>,
  ) => void;
  onGenerate: (isSegImage: boolean) => void;
  quotaUsage?: QuotaUsageResponse | null;
}

const ImageForm: React.FC<ImageFormProps> = ({ state, onUpdate, onGenerate, quotaUsage }) => {
  const [helpDrawerVisible, setHelpDrawerVisible] = useState(false);
  const [currentHelpContent, setCurrentHelpContent] = useState<HelpContent | null>(null);
  const [currentHelpKey, setCurrentHelpKey] = useState<string>('');
  const [isMatting, setIsMatting] = useState(false);
  // const [showCompositionEditor, setShowCompositionEditor] = useState(false);
  const isMobile = isMobileDevice();
  const isSegImageRef = useRef(true); // 是否是抠图后的图片

  // Auto extract image
  const autoExtractImage = async (originalImageUrl: string): Promise<string> => {
    try {
      // 调用抠图接口
      const response = await extractImage({ imgUrl: originalImageUrl?.replace('http://', 'https://') }) as any;

      if (response.success && response.imgUrl) {
        isSegImageRef.current = true;
        return response.imgUrl;
      } else {
        isSegImageRef.current = false;
        return originalImageUrl;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log('error', errorMessage);
      isSegImageRef.current = false;
      return originalImageUrl;
    }
  };

  // Help content data
  const helpContents: HelpContents = {
    imageUpload: {
      title: i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_ImageSpecification'),
      content: [
        {
          type: 'section',
          title: i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_ImageSize'),
          items: [
            i18next.t(
              'j-dingtalk-web_pages_ai-image_components_ImageForm_TheImageSizeCannotExceed',
            ),
            i18next.t(
              'j-dingtalk-web_pages_ai-image_components_ImageForm_ResolutionGreaterThanLessThan',
            ),
          ],
        },
        {
          type: 'section',
          title: i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_ShootingGuidance'),
          items: [
            i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_TheEdgeOfTheMain'),
            i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_ThereIsNoWatermarkIn'),
            i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_TheBackgroundIsCleanAnd'),
          ],
        },
        {
          type: 'examples',
          title: i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_CorrectExample'),
          status: 'correct',
          isTwoImage: true,
          images: [
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN01fc0Rav1moyCoFUNye_!!6000000005002-2-tps-231-154.png',
              label: i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_ClearSubject'),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i3/O1CN01waVLIR1a2d0pEmDR5_!!6000000003272-2-tps-242-161.png',
              label: i18next.t(
                'j-dingtalk-web_pages_ai-image_components_ImageForm_CleanBackground',
              ),
            },
          ],
        },
        {
          type: 'examples',
          title: i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_ErrorExample'),
          status: 'incorrect',
          isTwoImage: true,
          images: [
            {
              src: 'https://img.alicdn.com/imgextra/i2/O1CN01r1tTUY1bcBNEP33le_!!6000000003485-2-tps-249-166.png',
              label: i18next.t(
                'j-dingtalk-web_pages_ai-image_components_ImageForm_FuzzyBackground',
              ),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i1/O1CN01M4xPNG1wHfZ4Gi3DB_!!6000000006283-2-tps-231-154.png',
              label: i18next.t(
                'j-dingtalk-web_pages_ai-image_components_ImageForm_TheProductBodyIsNot',
              ),
            },
          ],
        },
      ],
    },
  };

  // Handle image upload
  const handleImageUpload = async (imageUrl: string | null) => {
    if (!imageUrl) {
      onUpdate({ uploadedImage: null });
      return;
    }

    sendUT('aigc_picture_image_upload', {
      device: isMobile ? 'mobile' : 'pc',
      imageUrl,
    });

    // 图片上传成功之后，执行自动抠图操作 (Auto extract image after upload success)
    try {
      setIsMatting(true);
      // 调用自动抠图功能 (Call auto extract image function)
      const processedImageUrl = await autoExtractImage(imageUrl);
      // 更新状态，使用处理后的图片URL (Update state with processed image URL)
      onUpdate({ uploadedImage: processedImageUrl });

      // 如果抠图成功（URL发生变化），显示成功提示 (Show success message if extraction succeeded)
      if (processedImageUrl !== imageUrl) {
        Toast.success({
          content: i18next.t(
            'j-dingtalk-web_pages_ai-image_components_ImageForm_ImageMattingCompleted',
          ),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
      }
    } catch (error) {
      // 如果发生异常，使用原图并显示提示 (Use original image and show message on error)
      console.error(
        i18next.t(
          'j-dingtalk-web_pages_ai-image_components_ImageForm_ImageProcessingExceptionImageProcessing',
        ),
        error,
      );
      onUpdate({ uploadedImage: imageUrl });

      Toast.info({
        content: i18next.t(
          'j-dingtalk-web_pages_ai-image_components_ImageForm_ImageProcessingFailedUseThe',
        ),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    } finally {
      setIsMatting(false);
    }
  };

  // Handle prompt change
  const handlePromptChange = (field: string, value: string) => {
    onUpdate({ [field]: value });
  };

  const handleCopyPrompt = (promptText: string) => {
    // Update positive prompt field
    onUpdate({ positivePrompt: promptText });

    // Show success message
    Toast.success({
      content: i18next.t(
        'j-dingtalk-web_pages_aiVideo_components_VideoForm_CopiedToCreativeDescription',
      ),
      duration: 2,
      position: 'top',
      maskClickable: true,
    });
  };

  // Handle help icon click
  const handleHelpClick = (helpKey: string) => {
    sendUT('aigc_picture_guide', {
      device: isMobile ? 'mobile' : 'pc',
      helpKey,
    });

    const helpContent = helpContents[helpKey];
    if (helpContent) {
      setCurrentHelpContent(helpContent);
      setCurrentHelpKey(helpKey);
      if (isMobile) {
        setHelpDrawerVisible(true);
      }
    }
  };

  // Handle background selection - 处理背景选择
  const handleBackgroundChange = (option: BackgroundOption | null) => {
    sendUT('aigc_picture_prompt_template', {
      device: isMobile ? 'mobile' : 'pc',
      prompt: option?.desc,
      index: option?.index,
    });

    if (option) {
      onUpdate({ positivePrompt: option.desc });
    }
  };

  // Handle size selection
  const handleSizeChange = (size: string) => {
    sendUT('aigc_picture_size', {
      device: isMobile ? 'mobile' : 'pc',
      size,
    });

    onUpdate({ imageSize: size });
  };

  // Handle form submission
  const handleSubmit = () => {
    onGenerate(isSegImageRef.current);
  };

  // // Handle composition editor
  // const handleOpenCompositionEditor = () => {
  //   setShowCompositionEditor(true);
  // };

  // const handleCloseCompositionEditor = () => {
  //   setShowCompositionEditor(false);
  // };

  // const handleCompositionSave = (imageDataUrl: string) => {
  //   // Update the uploaded image with the edited composition
  //   onUpdate({ uploadedImage: imageDataUrl });
  //   setShowCompositionEditor(false);
    
  //   Toast.success({
  //     content: i18next.t('j-dingtalk-web_components_CompositionEditor_ExportSuccess'),
  //     duration: 2,
  //   });
  // };

  // Form title component with help icon
  const FormTitle: React.FC<ImageHelpProps> = ({ title, helpKey, onlyTitle }) => {
    const helpContent = helpContents[helpKey];

    const InfoIcon = () => {
      return (
        <button className="example-btn" onClick={() => handleHelpClick(helpKey)}>
          <InformationThereOutlined />
          {i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_SpecificationReference')}
        </button>
      );
    };

    if (isMobile) {
      return (
        <div className="form-title">
          <span className="form-title-text">{title}</span>
          {!onlyTitle && <InfoIcon />}
        </div>
      );
    } else {
      // PC端：使用 Popover 组件，鼠标悬停显示在右边
      const renderHelpContent = () => (
        <div className="help-popover-content">
          {helpContent?.content.map((item: HelpContentItem, index: number) => {
            const renderContent = () => {
              if (item.type === 'text') {
                return <p className="help-text">{item.text}</p>;
              }

              if (item.type === 'image') {
                return <img src={item.src} alt={item.alt} className="help-content-image" />;
              }

              if (item.type === 'section') {
                return (
                  <div className="help-section">
                    {item.items && item.items.length > 0 ? (
                      <>
                        <h4 className="help-section-title">{item.title}</h4>
                        <ul className="help-section-list">
                          {item.items.map((listItem, listIndex) => (
                            <li key={`section-item-${listIndex}`} className="help-section-item">
                              {listItem}
                            </li>
                          ))}
                        </ul>
                      </>
                    ) : (
                      <p className="help-section-description">{item.title}</p>
                    )}
                  </div>
                );
              }

              if (item.type === 'examples') {
                return (
                  <div
                    className={`help-examples ${
                      item.status === 'correct' ? 'correct' : 'incorrect'
                    }`}
                  >
                    <div className="help-examples-header">
                      <span
                        className={`help-examples-icon ${
                          item.status === 'correct' ? 'correct' : 'incorrect'
                        }`}
                      >
                        {item.status === 'correct' ? <CheckCircleFilled /> : <CloseCircledFilled />}
                      </span>
                      <h4
                        className={`help-examples-title ${
                          item.status === 'correct' ? 'correct' : 'incorrect'
                        }`}
                      >
                        {item.title}
                      </h4>
                    </div>
                    <div
                      className={`help-examples-grid ${
                        helpKey === 'positivePrompt' ? 'single-column' : ''
                      } ${item.isTwoImage ? 'force-two-columns' : ''}`}
                    >
                      {item.images?.map((image, imageIndex) => (
                        <div key={`example-image-${imageIndex}`} className="help-example-item">
                          {image.alt && (
                            <div className="help-example-copy-container">
                              <button
                                className="help-example-copy-btn"
                                onClick={() => handleCopyPrompt(image.alt!)}
                                title={i18next.t(
                                  'j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription',
                                )}
                              >
                                <CopyOutlined className="copy-icon" />
                              </button>
                            </div>
                          )}
                          <div className="help-example-image-placeholder">
                            <img
                              src={image.src}
                              alt={image.alt}
                              className="help-example-image"
                              loading="lazy"
                            />

                            <div className="help-example-label-overlay">
                              <p className="help-example-label">{image.label}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              }
              return null;
            };
            return (
              <div key={`help-content-${index}`} className="help-content-item">
                {renderContent()}
              </div>
            );
          })}
        </div>
      );

      return (
        <div className="form-title">
          <span className="form-title-text">{title}</span>
          {!onlyTitle && (
            <Popover
              title={helpContent?.title}
              content={renderHelpContent()}
              placement="right"
              trigger="hover"
              arrow={false}
              overlayClassName="form-help-popover"
            >
              <button className="example-btn">
                <InformationThereOutlined />
                {i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_SpecificationReference')}
              </button>
            </Popover>
          )}
        </div>
      );
    }
  };

  return (
    <div className="image-form">
      <div className="form-container">
        {/* Image Upload Section */}
        <div className="form-section">
          <FormTitle
            title={i18next.t(
              'j-dingtalk-web_pages_ai-image_components_ImageForm_UploadTheOriginalImage',
            )}
            helpKey="imageUpload"
          />

          <ImageUploader
            value={state.uploadedImage}
            onChange={handleImageUpload}
            sizeConstraints={{ minWidth: 515, minHeight: 515, maxWidth: 1595, maxHeight: 1595 }}
            scanningLoading={isMatting}
            hasCutBackground
          />

          {/* Composition Editor Button */}
          {/* {state.uploadedImage && (
            <div className="composition-editor-button">
              <Button
                type="primary"
                size="small"
                onClick={handleOpenCompositionEditor}
                className="open-editor-btn"
              >
                {i18next.t('j-dingtalk-web_components_CompositionEditor_OpenEditor')}
              </Button>
            </div>
          )} */}
        </div>

        {/* Positive Prompt Section */}
        <div className="form-section">
          <FormTitle
            title={i18next.t(
              'j-dingtalk-web_pages_ai-image_components_ImageForm_YourCreativeDescription',
            )}
            onlyTitle
          />

          <div className="textarea-container">
            <textarea
              className="custom-textarea"
              value={state.positivePrompt}
              placeholder={i18next.t(
                'j-dingtalk-web_pages_ai-image_components_ImageForm_SelectTheTemplateBelowOr',
              )}
              onChange={(e) => handlePromptChange('positivePrompt', e.target.value)}
              maxLength={200}
            />

            <div className="char-count">{state.positivePrompt.length} / 200</div>
          </div>
        </div>

        {/* Background Selection Section */}
        <div className="form-section">
          <BackgroundSelector onChange={handleBackgroundChange} />
        </div>

        {/* Size Selection Section */}
        <div className="form-section none">
          <div className="duration-section">
            <div className="duration-label">
              {i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_PictureSize')}
            </div>
            <SizeSelector value={state.imageSize} onChange={handleSizeChange} />
          </div>
        </div>

        {/* Error Display */}
        {state.error && <div className="error-message">{state.error}</div>}

        {/* Quota Information */}
        {quotaUsage && (
          <div className="quota-info">
            <div className="quota-item">
              {i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_ThisTimeWillConsume')}
            </div>
            <div className="quota-divider" />
            <div className={`quota-item ${quotaUsage && ((quotaUsage.totalQuota || 0) - (quotaUsage.usedQuota || 0)) === 0 ? 'quota-zero' : ''}`}>
              {i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_UsageCount')}
              <QuotaDisplay quotaUsage={quotaUsage} />
            </div>
          </div>
        )}

        {/* Generate Button */}
        <div className="form-actions">
          <Button
            type="primary"
            size="large"
            className="generate-btn"
            loading={state.isGenerating}
            onClick={handleSubmit}
            disabled={!state.uploadedImage || !state.positivePrompt.trim() || state.isGenerating}
          >
            {state.isGenerating
              ? i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_Generating')
              : i18next.t('j-dingtalk-web_pages_ai-image_components_ImageForm_GenerateNow')}
          </Button>
        </div>
      </div>
      {/* Help Drawer for Mobile */}
      {isMobile && (
        <ConfigProvider config={{ adapt: false }}>
          <Drawer
            size="large"
            title={
              currentHelpContent?.title ||
              i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_HelpInformation')
            }
            visible={helpDrawerVisible}
            onVisibleChange={setHelpDrawerVisible}
          >
            <div className="help-drawer-content">
              {currentHelpContent?.content.map((item: HelpContentItem, index: number) => {
                const renderDrawerContent = () => {
                  if (item.type === 'text') {
                    return <p className="help-text">{item.text}</p>;
                  }
                  if (item.type === 'image') {
                    return <img src={item.src} alt={item.alt} className="help-content-image" />;
                  }
                  if (item.type === 'section') {
                    return (
                      <div className="help-section">
                        {item.items && item.items.length > 0 ? (
                          <>
                            <h4 className="help-section-title">{item.title}</h4>
                            <ul className="help-section-list">
                              {item.items.map((listItem, listIndex) => (
                                <li
                                  key={`drawer-section-item-${listIndex}`}
                                  className="help-section-item"
                                >
                                  {listItem}
                                </li>
                              ))}
                            </ul>
                          </>
                        ) : (
                          <p className="help-section-description">{item.title}</p>
                        )}
                      </div>
                    );
                  }

                  if (item.type === 'examples') {
                    return (
                      <div
                        className={`help-examples ${
                          item.status === 'correct' ? 'correct' : 'incorrect'
                        }`}
                      >
                        <div className="help-examples-header">
                          <span
                            className={`help-examples-icon ${
                              item.status === 'correct' ? 'correct' : 'incorrect'
                            }`}
                          >
                            {item.status === 'correct' ? (
                              <CheckCircleFilled />
                            ) : (
                              <CloseCircledFilled />
                            )}
                          </span>
                          <h4
                            className={`help-examples-title ${
                              item.status === 'correct' ? 'correct' : 'incorrect'
                            }`}
                          >
                            {item.title}
                          </h4>
                        </div>
                        <div
                          className={`help-examples-grid ${
                            currentHelpKey === 'positivePrompt' ? 'single-column' : ''
                          } ${item.isTwoImage ? 'force-two-columns' : ''}`}
                        >
                          {item.images?.map((image, imageIndex) => (
                            <div
                              key={`drawer-example-image-${imageIndex}`}
                              className="help-example-item"
                            >
                              {image.alt && (
                                <div className="help-example-copy-container">
                                  <button
                                    className="help-example-copy-btn"
                                    onClick={() => handleCopyPrompt(image.alt!)}
                                    title={i18next.t(
                                      'j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription',
                                    )}
                                  >
                                    <CopyOutlined className="copy-icon" />
                                  </button>
                                </div>
                              )}
                              <div className="help-example-image-placeholder">
                                <img
                                  src={image.src}
                                  alt={image.alt}
                                  className="help-example-image"
                                  loading="lazy"
                                />
                                <div className="help-example-label-overlay">
                                  <p className="help-example-label">{image.label}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  }
                  return null;
                };

                return (
                  <div key={`drawer-help-content-${index}`} className="help-content-item">
                    {renderDrawerContent()}
                  </div>
                );
              })}
            </div>
          </Drawer>
        </ConfigProvider>
      )}

      {/* Composition Editor Modal */}
      {/* <CompositionEditor
        visible={showCompositionEditor}
        onClose={handleCloseCompositionEditor}
        imageUrl={state.uploadedImage || ''}
        onSave={handleCompositionSave}
      /> */}
    </div>
  );
};

export default ImageForm;
