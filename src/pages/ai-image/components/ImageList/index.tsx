/* eslint-disable react/prop-types */
import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useRef, useCallback, memo } from 'react';
import { Button } from 'dingtalk-design-mobile';
import { RefreshOutlined, EditOutlined } from '@ali/ding-icons';
import { isDingTalk, isMobileDevice, setPageTitle } from '@/utils/jsapi';
import { ImageInfo } from '../../types';
import ImageItem from '../ImageItem';
import './index.less';

interface ImageListProps {
  imageList: ImageInfo[];
  isLoading: boolean;
  error: string | null;
  onCreateNew: () => void;
  onRegenerate?: (imageInfo: ImageInfo) => void;
  onRefresh?: () => void;
  className?: string;
  progressMap?: Record<string, number>; // Map of image UUID to progress percentage
  hasNextPage?: boolean; // Whether there are more pages to load
  isLoadingMore?: boolean; // Loading state for pagination
  onLoadMore?: () => void; // Function to load more images
  loadImageList: () => void; // Load image list
  onOptimizedImageUpdate?: (
    regenerateResult: any,
    originalUuid: string)
  => Promise<void>; // Optimized image update
}

const ImageList: React.FC<ImageListProps> = memo(({
  imageList,
  isLoading,
  error,
  onCreateNew,
  onRegenerate,
  onRefresh,
  className = '',
  progressMap = {},
  hasNextPage = false,
  isLoadingMore = false,
  onLoadMore,
  loadImageList,
  onOptimizedImageUpdate,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<number | null>(null);

  // Optimized scroll handler with better performance
  const handleScrollThrottled = useCallback(() => {
    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
      scrollTimeoutRef.current = null;
    }

    scrollTimeoutRef.current = window.setTimeout(() => {
      // Check if component is still mounted and conditions are valid
      if (!containerRef.current || !hasNextPage || isLoadingMore || !onLoadMore) {
        return;
      }

      let scrollTop: number;
      let scrollHeight: number;
      let clientHeight: number;

      if (isMobileDevice()) {
        // Mobile: use window scroll
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.documentElement.scrollHeight;
        clientHeight = window.innerHeight;
      } else {
        // PC: use right panel scroll (parent container)
        const rightPanel = containerRef.current?.closest('.ai-image-right-panel') as HTMLElement;
        if (!rightPanel) return;

        scrollTop = rightPanel.scrollTop;
        scrollHeight = rightPanel.scrollHeight;
        clientHeight = rightPanel.clientHeight;
      }

      // Trigger load more when scrolled to 80% of the content
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      if (scrollPercentage >= 0.8) {
        onLoadMore();
      }
    }, 300); // Increased to 300ms for better performance on iOS
  }, [hasNextPage, isLoadingMore, onLoadMore]);

  useEffect(() => {
    setPageTitle(i18next.t('j-dingtalk-web_pages_ai-image_AiImageGeneration'));
    // Remove the $setRight call - this is now handled by the parent component through MobileNavbar
  }, []);

  // Add scroll listener with proper cleanup
  useEffect(() => {
    let scrollElement: HTMLElement | Window | null = null;
    if (isMobileDevice()) {
      // Mobile: listen to window scroll
      scrollElement = window;
      window.addEventListener('scroll', handleScrollThrottled, { passive: true });
    } else {
      // PC: listen to right panel scroll
      const rightPanel = containerRef.current?.closest('.ai-image-right-panel') as HTMLElement;
      if (rightPanel) {
        scrollElement = rightPanel;
        rightPanel.addEventListener('scroll', handleScrollThrottled, { passive: true });
      }
    }

    return () => {
      // Cleanup scroll listener
      if (scrollElement) {
        if (scrollElement === window) {
          window.removeEventListener('scroll', handleScrollThrottled);
        } else {
          (scrollElement as HTMLElement).removeEventListener('scroll', handleScrollThrottled);
        }
      }

      // Cleanup timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = null;
      }
    };
  }, [handleScrollThrottled]);

  // Additional cleanup on unmount - redundant but safe
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = null;
      }
    };
  }, []);

  // Render empty state
  const renderEmptyState = () =>
    (
      <div className="image-list-empty">
        <div className="empty-icon">🖼️</div>
        <div className="empty-title">{i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_NoDataAvailable')}</div>
        <div className="empty-description">{i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_StartCreatingYourFirstAi')}</div>
        <Button
          type="primary"
          size="large"
          className="create-first-image-btn"
          onClick={onCreateNew}
        >
          {i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_CreateAnImage')}
        </Button>
      </div>
    );


  // Render error state
  const renderErrorState = () =>
    (
      <div className="image-list-error">
        <div className="error-icon">❌</div>
        <div className="error-title">{i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_FailedToLoad')}</div>
        <div className="error-description">
          {error || i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_FailedToObtainTheImage')}
        </div>
        <div className="error-actions">
          {onRefresh &&
          <Button
            type="secondary"
            size="middle"
            onClick={onRefresh}
            className="retry-btn"
          >
            <RefreshOutlined />{i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_Retry')}
          </Button>
          }
          <Button
            type="primary"
            size="middle"
            onClick={onCreateNew}
            className="create-new-btn"
          >
            {i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_CreateAnImage')}
          </Button>
        </div>
      </div>
    );

  // Render loading state
  const renderLoadingState = () =>

    (
      <div className="image-list-loading">
        <div className="loading-spinner" />
        <div className="loading-text">{i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_Loading')}</div>
      </div>
    );

  // Render image list content
  const renderImageList = () =>
    (
      <div className="image-list-content">
        {/* Image list with optimized rendering */}
        <div className="image-list-items" ref={containerRef}>
          {imageList.map((imageInfo) => {
            // Memoize progress to avoid unnecessary re-renders
            const progress = progressMap[imageInfo.uuid] || 0;

            return (
              <ImageItem
                key={imageInfo.uuid}
                imageInfo={imageInfo}
                onRegenerate={onRegenerate || (() => {})}
                loadImageList={loadImageList}
                className="image-list-item"
                progress={progress}
                onOptimizedImageUpdate={onOptimizedImageUpdate}
              />
            );
          })}
        </div>

        {/* Load more section */}
        {imageList.length > 0 &&
        <div className="load-more-section">
          {isLoadingMore &&
          <div className="load-more-loading">
            <div className="loading-spinner" />
            <div className="loading-text">{i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_Loading')}</div>
          </div>
          }
          {!isLoadingMore && !hasNextPage &&
          <div className="load-more-text">{i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_AllDataDisplayed')}</div>
          }
          {!isLoadingMore && hasNextPage &&
          <div className="load-more-text">{i18next.t('j-dingtalk-web_pages_ai-image_components_ImageList_SlideDownToLoadMore')}</div>
          }
        </div>
        }
      </div>
    );

  return (
    <div className={`image-list ${className}`}>
      {isLoading && renderLoadingState()}
      {!isLoading && error && renderErrorState()}
      {!isLoading && !error && imageList.length === 0 && renderEmptyState()}
      {!isLoading && !error && imageList.length > 0 && renderImageList()}
    </div>
  );
});

ImageList.displayName = 'ImageList';

export default ImageList;
