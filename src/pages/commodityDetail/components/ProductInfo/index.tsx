import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { decode } from 'he';
import { EPlatform, Product } from '@/common/types';
import ZozoComments from '../ZozoComments';
import AmazonComments from '../AmazonComments';
import { Drawer, ConfigProvider } from 'dingtalk-design-mobile';
import Comments from '@/pages/comments';
import { getProductComment } from '@/apis';

import './style.less';

interface ProductInfoProps {
  product: Product;
  pageType?: string;
  platform: EPlatform;
  onClick: () => void;
}

const ProductInfo: React.FC<ProductInfoProps> = ({ product, pageType, platform, onClick }) => {
  const [commVisible, setCommVisible] = React.useState(false);
  const [commentsData, setCommentsData] = React.useState(null);
  const handleCommVisible = (v: boolean, productId?: number, sourceItemId?: number) => {
    setCommVisible(v);
    if (productId) {
      getProductComment({
        productId: productId.toString(),
        sourceItemId: sourceItemId?.toString(),
        platform,
      }).then((res) => {
        setCommentsData(res);
      });
    }
  };

  return (
    <div className="product-info">
      <div
        className="product-info-container"
        onClick={onClick}
      >
        <div className="product-title">{product.title}</div>
        <div className="product-price-container">
          <div className="price-section">
            <span className="price">
              {product.price}
              <span className="unit">{i18next.t('j-dingtalk-web_pages_create-order_Currency')}</span>
            </span>
            {
              product.originalPrice &&
              <span className="original-price">
                ({product.originalPrice})
                <span className="unit">{i18next.t('j-dingtalk-web_pages_create-order_Currency')}</span>
              </span>

            }
          </div>
        </div>

        {
          product.tagArr?.length > 0 && (
            <div className="product-stats">
              {product.tagArr?.map((tag) =>
                (
                  <div key={tag} className="tag">
                    <span className="value">{tag}</span>
                  </div>
                ))}
            </div>
          )
        }
        {
          product?.storeName && (
            <div className="product-bottom">
              <div className="store-name">
                {`${i18next.t('j-agent-web_pages_commodityDetail_components_ProductInfo_StoreNameProductstorename')}${decode(product?.storeName || '')}`}
              </div>
            </div>
          )
        }
      </div>
      {
        pageType === 'RankingList' && product.commentReport && (
          <>
            {
              platform === EPlatform.AMAZON ? (
                <AmazonComments
                  data={product}
                  pageType={pageType}
                  setDrawerVisible={handleCommVisible}
                  platform={platform}
                  showMore={false}
                />
              ) : (
                <ZozoComments
                  data={product}
                  pageType={pageType}
                  setDrawerVisible={handleCommVisible}
                  platform={platform}
                />
              )
            }

            <ConfigProvider config={{ adapt: false }}>
              <Drawer
                size="large"
                title={i18next.t('j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CommentDetails')}
                visible={commVisible}
                onVisibleChange={(v) => {
                  setCommVisible(v);
                  if (!v) {
                    setCommentsData(null);
                  }
                }}
              >
                {
                  <div style={{ padding: '16px 16px 0' }}>
                    {
                      platform === EPlatform.AMAZON ? (
                        <AmazonComments
                          data={product}
                          pageType={pageType}
                          platform={platform}
                          showMore
                        />
                      ) : (
                        <ZozoComments
                          data={product}
                          pageType={pageType}
                          platform={platform}
                          showTitle={false}
                        />
                      )
                    }
                  </div>
                }
                <Comments
                  commentsData={commentsData}
                  platform={platform}
                />
              </Drawer>
            </ConfigProvider>
          </>
        )
      }
    </div>);
};

export default ProductInfo;
