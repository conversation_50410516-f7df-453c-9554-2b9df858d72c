export enum Language {
  zh_CN = 'zh_CN', // 简体中文
  zh_TW = 'zh_TW', // 繁体中文（台湾）
  zh_HK = 'zh_HK', // 繁体中文（香港）
  en_US = 'en_US', // 英文
  ja_JP = 'ja_JP', // 日文
  vi_VN = 'vi_VN', // 越南语
  th_TH = 'th_TH', // 泰语
  ne_NP = 'ne_NP', // 尼泊尔语
  id_ID = 'id_ID', // 印尼语
  ms_MY = 'ms_MY', // 马来语
  ko_KR = 'ko_KR', // 朝鲜语，韩语
}

const getCookie = (name: string) => {
  try {
    const arr = document.cookie.match(new RegExp(`(^| )${name}=([^;]*)(;|$)`));
    return arr ? arr[2] : '';
  } catch (e) {
    return '';
  }
};

// 从navigator中获取语言
const getLanguageFromNavigator = () => {
  return navigator.language;
};

// 从cookie里获取语言码 dd_l=zh-cn 或者 SSO_LANG_V2=ZH-CN（专有钉web环境）
const getLanguageFromCookie = () => {
  return getCookie('dd_l') || (getCookie('SSO_LANG_V2') || '')?.toLowerCase() || '';
};

// 从window.name里获取语言码 window.name = '{"language":"zh-cn"}'
const getLanguageFromWindowName = () => {
  let result = '';
  try {
    const nameObj = JSON.parse(window.name);
    result = (nameObj && nameObj.language) || '';
  } catch (e) {
    // empty
  }
  return result;
};

// 从UserAgent里获取语言码 language/zh-cn TaurusLanguage/zh_CN
const getLanguageFromUserAgent = () => {
  const matches = navigator.userAgent.match(/language\/(\S+)/i);
  return (matches && matches[1]) || '';
};

// 获得语言枚举值，对于不支持的语言码，会返回默认语言英文。
const getStandardLanguage = (languageCode: string) => {
  const language: string[] = languageCode?.toLowerCase().split(/[-_]/g);
  const langDir: { [key: string]: Language } = {
    zh: Language.zh_CN,
    en: Language.en_US,
    zh_tw: Language.zh_TW,
    zh_hk: Language.zh_HK,
    zh_hant: Language.zh_TW,
    zh_hant_hk: Language.zh_HK,
    ja: Language.ja_JP,
    vi: Language.vi_VN,
    th: Language.th_TH,
    ne: Language.ne_NP,
    id: Language.id_ID,
    ms: Language.ms_MY,
    ko: Language.ko_KR,
  };

  // 从后向前匹配语言码，直至第一位语言码，如果仍然无法匹配，返回默认语言英文 Language.zh_CN
  while (language.length) {
    const key = language.join('_');
    if (langDir[key]) {
      return langDir[key];
    }
    language.pop();
  }

  return Language.zh_CN;
};

// 获取当前环境语言，基于钉钉内部的语言配置方式，优先级分别是：
// userAgent：最高优先级，场景为钉钉客户端内，与客户端保持一致
// window.name：第二优先级，适用于嵌入的 web 页面，比如 iframe，通过传递 window.name 保持语言一致
// cookie：纯 web 场景，钉钉在纯 web 环境会配置 cookie 记录用户选择语言。
// navigator.language：浏览器设置
export const getCurrentLanguage = () => {
  return getStandardLanguage(
    getLanguageFromUserAgent() ||
      getLanguageFromWindowName() ||
      getLanguageFromCookie() ||
      getLanguageFromNavigator() ||
      '',
  );
};
